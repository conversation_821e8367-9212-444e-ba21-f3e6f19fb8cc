// API service for communicating with the backend
class APIService {
    constructor() {
        this.baseURL = CONFIG.API_BASE_URL;
    }

    // Generic request method
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error(`API request failed: ${endpoint}`, error);
            throw error;
        }
    }

    // GET request
    async get(endpoint) {
        return this.request(endpoint, { method: 'GET' });
    }

    // POST request
    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT request
    async put(endpoint, data) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE request
    async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }

    // Bus-related API calls
    async getAllBuses() {
        return this.get('/buses/');
    }

    async getBus(busId) {
        return this.get(`/buses/${busId}`);
    }

    async getBusLocation(busId) {
        return this.get(`/buses/${busId}/location`);
    }

    async getBusLocationHistory(busId, hours = 24) {
        return this.get(`/buses/${busId}/location/history?hours=${hours}`);
    }

    async getBusStatus(busId) {
        return this.get(`/buses/${busId}/status`);
    }

    async updateBusLocation(busId, locationData) {
        return this.post(`/buses/${busId}/location`, locationData);
    }

    async startBusSimulation(busId, speedKmh = 30) {
        return this.post(`/buses/${busId}/simulation/start`, { speed_kmh: speedKmh });
    }

    async stopBusSimulation(busId) {
        return this.post(`/buses/${busId}/simulation/stop`);
    }

    async getBusesOnRoute(routeId) {
        return this.get(`/buses/route/${routeId}`);
    }

    async getNearbyBuses(latitude, longitude, radiusKm = 5) {
        return this.get(`/buses/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radiusKm}`);
    }

    // Route-related API calls
    async getAllRoutes() {
        return this.get('/routes/');
    }

    async getRoute(routeId) {
        return this.get(`/routes/${routeId}`);
    }

    async getRouteStops(routeId) {
        return this.get(`/routes/${routeId}/stops`);
    }

    async getRouteSchedule(routeId) {
        return this.get(`/routes/${routeId}/schedule`);
    }

    async searchRoutes(query) {
        return this.get(`/routes/search?q=${encodeURIComponent(query)}`);
    }

    // Stop-related API calls
    async getAllStops() {
        return this.get('/stops/');
    }

    async getStop(stopId) {
        return this.get(`/stops/${stopId}`);
    }

    async getStopArrivals(stopId, limit = 5) {
        return this.get(`/stops/${stopId}/arrivals?limit=${limit}`);
    }

    async getNearbyStops(latitude, longitude, radiusKm = 1) {
        return this.get(`/stops/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radiusKm}`);
    }

    async searchStops(query) {
        return this.get(`/stops/search?q=${encodeURIComponent(query)}`);
    }

    // Tracking-related API calls
    async getLiveTracking() {
        return this.get('/tracking/live');
    }

    async getRouteTracking(routeId) {
        return this.get(`/tracking/route/${routeId}`);
    }

    async getArrivalPredictions(stopId, limit = 5) {
        return this.get(`/tracking/arrivals/${stopId}?limit=${limit}`);
    }

    async calculateETA(busId, stopId) {
        return this.post('/tracking/eta', { bus_id: busId, stop_id: stopId });
    }

    // Passenger-related API calls
    async registerPassenger(passengerData) {
        return this.post('/passengers/register', passengerData);
    }

    async getPassenger(passengerId) {
        return this.get(`/passengers/${passengerId}`);
    }

    async createReservation(reservationData) {
        return this.post('/passengers/reservations', reservationData);
    }

    async getReservation(reservationCode) {
        return this.get(`/passengers/reservations/${reservationCode}`);
    }

    async cancelReservation(reservationCode) {
        return this.post(`/passengers/reservations/${reservationCode}/cancel`);
    }

    async getPassengerReservations(passengerId) {
        return this.get(`/passengers/${passengerId}/reservations`);
    }

    // Alert-related API calls
    async getActiveAlerts() {
        return this.get('/alerts/');
    }

    async createAlert(alertData) {
        return this.post('/alerts/', alertData);
    }

    async resolveAlert(alertId, resolvedBy) {
        return this.post(`/alerts/${alertId}/resolve`, { resolved_by: resolvedBy });
    }

    async getEmergencyAlerts() {
        return this.get('/alerts/emergency');
    }

    async createEmergencyAlert(emergencyData) {
        return this.post('/alerts/emergency', emergencyData);
    }

    async resolveEmergencyAlert(alertId, resolutionNotes) {
        return this.post(`/alerts/emergency/${alertId}/resolve`, { resolution_notes: resolutionNotes });
    }

    async getRouteAlerts(routeId) {
        return this.get(`/alerts/route/${routeId}`);
    }

    async getStopAlerts(stopId) {
        return this.get(`/alerts/stop/${stopId}`);
    }
}

// WebSocket service for real-time updates
class WebSocketService {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
    }

    connect() {
        try {
            this.socket = io(CONFIG.WEBSOCKET_URL);
            
            this.socket.on('connect', () => {
                console.log('WebSocket connected');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                UTILS.showNotification('Connected to real-time updates', 'success');
            });

            this.socket.on('disconnect', () => {
                console.log('WebSocket disconnected');
                this.isConnected = false;
                this.attemptReconnect();
            });

            this.socket.on('bus_location_update', (data) => {
                this.handleBusLocationUpdate(data);
            });

            this.socket.on('bus_status_update', (data) => {
                this.handleBusStatusUpdate(data);
            });

            this.socket.on('alert_update', (data) => {
                this.handleAlertUpdate(data);
            });

        } catch (error) {
            console.error('WebSocket connection failed:', error);
            this.attemptReconnect();
        }
    }

    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
            this.isConnected = false;
        }
    }

    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
            setTimeout(() => {
                this.connect();
            }, this.reconnectDelay * this.reconnectAttempts);
        } else {
            console.error('Max reconnection attempts reached');
            UTILS.showNotification('Connection lost. Please refresh the page.', 'error');
        }
    }

    subscribeToBus(busId) {
        if (this.isConnected) {
            this.socket.emit('subscribe_bus', { bus_id: busId });
        }
    }

    subscribeToRoute(routeId) {
        if (this.isConnected) {
            this.socket.emit('subscribe_route', { route_id: routeId });
        }
    }

    handleBusLocationUpdate(data) {
        // Emit custom event for components to listen to
        window.dispatchEvent(new CustomEvent('busLocationUpdate', { detail: data }));
    }

    handleBusStatusUpdate(data) {
        window.dispatchEvent(new CustomEvent('busStatusUpdate', { detail: data }));
    }

    handleAlertUpdate(data) {
        window.dispatchEvent(new CustomEvent('alertUpdate', { detail: data }));
    }
}

// Create global instances
const api = new APIService();
const websocket = new WebSocketService();
