// Configuration settings for the bus tracking application
const CONFIG = {
    // API Base URL
    API_BASE_URL: 'http://localhost:5000/api',
    
    // WebSocket URL
    WEBSOCKET_URL: 'http://localhost:5000',
    
    // Map Configuration
    MAP: {
        DEFAULT_CENTER: [12.9716, 77.5946], // Bangalore coordinates
        DEFAULT_ZOOM: 13,
        MAX_ZOOM: 18,
        MIN_ZOOM: 10,
        TILE_LAYER: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        ATTRIBUTION: '© OpenStreetMap contributors'
    },
    
    // Update Intervals (in milliseconds)
    UPDATE_INTERVALS: {
        LIVE_TRACKING: 30000,    // 30 seconds
        ARRIVALS: 60000,         // 1 minute
        ALERTS: 120000           // 2 minutes
    },
    
    // Search Configuration
    SEARCH: {
        MIN_QUERY_LENGTH: 2,
        DEBOUNCE_DELAY: 300,     // milliseconds
        MAX_RESULTS: 10
    },
    
    // Geolocation Configuration
    GEOLOCATION: {
        TIMEOUT: 10000,          // 10 seconds
        MAX_AGE: 300000,         // 5 minutes
        HIGH_ACCURACY: true
    },
    
    // Map Icons
    ICONS: {
        BUS: {
            iconUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTZIMjBWNkg0VjE2Wk02IDEwSDhWMTJINlYxMFpNMTAgMTBIMTJWMTJIMTBWMTBaTTE0IDEwSDE2VjEySDEzVjEwWk0xOCAxMEgyMFYxMkgxOFYxMFoiIGZpbGw9IiMzNDk4ZGIiLz4KPC9zdmc+',
            iconSize: [24, 24],
            iconAnchor: [12, 12]
        },
        STOP: {
            iconUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTAiIGN5PSIxMCIgcj0iOCIgZmlsbD0iIzI3YWU2MCIvPgo8Y2lyY2xlIGN4PSIxMCIgY3k9IjEwIiByPSI0IiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4=',
            iconSize: [20, 20],
            iconAnchor: [10, 10]
        },
        USER: {
            iconUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTAiIGN5PSIxMCIgcj0iOCIgZmlsbD0iI2U3NGMzYyIvPgo8Y2lyY2xlIGN4PSIxMCIgY3k9IjEwIiByPSI0IiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4=',
            iconSize: [20, 20],
            iconAnchor: [10, 10]
        }
    },
    
    // Colors
    COLORS: {
        PRIMARY: '#3498db',
        SUCCESS: '#27ae60',
        WARNING: '#f39c12',
        DANGER: '#e74c3c',
        INFO: '#17a2b8',
        LIGHT: '#f8f9fa',
        DARK: '#343a40'
    },
    
    // Bus Status Colors
    BUS_STATUS_COLORS: {
        'ON_ROUTE': '#27ae60',
        'AT_STOP': '#f39c12',
        'BREAK': '#95a5a6',
        'MAINTENANCE': '#e74c3c',
        'OUT_OF_SERVICE': '#6c757d'
    },
    
    // Alert Types
    ALERT_TYPES: {
        'INFO': {
            color: '#3498db',
            icon: 'fas fa-info-circle'
        },
        'WARNING': {
            color: '#f39c12',
            icon: 'fas fa-exclamation-triangle'
        },
        'EMERGENCY': {
            color: '#e74c3c',
            icon: 'fas fa-exclamation-circle'
        },
        'MAINTENANCE': {
            color: '#95a5a6',
            icon: 'fas fa-tools'
        }
    },
    
    // Local Storage Keys
    STORAGE_KEYS: {
        USER_LOCATION: 'userLocation',
        FAVORITE_STOPS: 'favoriteStops',
        FAVORITE_ROUTES: 'favoriteRoutes',
        SETTINGS: 'appSettings'
    },
    
    // Default Settings
    DEFAULT_SETTINGS: {
        notifications: true,
        autoRefresh: true,
        showTraffic: false,
        language: 'en',
        theme: 'light'
    }
};

// Utility functions
const UTILS = {
    // Format time
    formatTime: (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
        });
    },
    
    // Format distance
    formatDistance: (km) => {
        if (km < 1) {
            return `${Math.round(km * 1000)}m`;
        }
        return `${km.toFixed(1)}km`;
    },
    
    // Calculate time difference in minutes
    getMinutesFromNow: (dateString) => {
        const now = new Date();
        const target = new Date(dateString);
        return Math.round((target - now) / (1000 * 60));
    },
    
    // Debounce function
    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Show notification
    showNotification: (message, type = 'info') => {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    },
    
    // Get user's current location
    getCurrentLocation: () => {
        return new Promise((resolve, reject) => {
            if (!navigator.geolocation) {
                reject(new Error('Geolocation is not supported'));
                return;
            }
            
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const location = {
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                        accuracy: position.coords.accuracy
                    };
                    
                    // Save to local storage
                    localStorage.setItem(
                        CONFIG.STORAGE_KEYS.USER_LOCATION,
                        JSON.stringify(location)
                    );
                    
                    resolve(location);
                },
                (error) => {
                    reject(error);
                },
                CONFIG.GEOLOCATION
            );
        });
    },
    
    // Get saved user location
    getSavedLocation: () => {
        const saved = localStorage.getItem(CONFIG.STORAGE_KEYS.USER_LOCATION);
        return saved ? JSON.parse(saved) : null;
    }
};
