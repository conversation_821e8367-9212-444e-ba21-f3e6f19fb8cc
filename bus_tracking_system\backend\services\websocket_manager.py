"""
Advanced WebSocket manager for real-time communication
"""

import asyncio
import json
from typing import Dict, Set, Any, Optional, List
from datetime import datetime, timedelta
import structlog

from fastapi import WebSocket, WebSocketDisconnect
from pydantic import BaseModel, ValidationError

from backend.core.redis_client import get_pubsub, get_cache
from backend.core.config import get_settings

logger = structlog.get_logger()


class WebSocketMessage(BaseModel):
    """WebSocket message schema"""
    type: str
    data: Dict[str, Any]
    timestamp: Optional[datetime] = None
    client_id: Optional[str] = None


class ConnectionInfo(BaseModel):
    """Connection information"""
    client_id: str
    websocket: WebSocket
    connected_at: datetime
    last_ping: datetime
    subscriptions: Set[str] = set()
    user_id: Optional[str] = None
    device_type: Optional[str] = None
    location: Optional[Dict[str, float]] = None


class WebSocketManager:
    """Advanced WebSocket connection manager"""
    
    def __init__(self):
        self.active_connections: Dict[str, ConnectionInfo] = {}
        self.room_subscriptions: Dict[str, Set[str]] = {}  # room -> client_ids
        self.client_subscriptions: Dict[str, Set[str]] = {}  # client_id -> rooms
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        self.settings = get_settings()
    
    async def connect(self, websocket: WebSocket, client_id: str) -> None:
        """Accept new WebSocket connection"""
        try:
            await websocket.accept()
            
            # Check connection limit
            if len(self.active_connections) >= self.settings.WS_MAX_CONNECTIONS:
                await websocket.close(code=1013, reason="Too many connections")
                return
            
            # Store connection info
            connection_info = ConnectionInfo(
                client_id=client_id,
                websocket=websocket,
                connected_at=datetime.utcnow(),
                last_ping=datetime.utcnow()
            )
            
            self.active_connections[client_id] = connection_info
            self.client_subscriptions[client_id] = set()
            
            # Send welcome message
            await self.send_personal_message(client_id, {
                "type": "connection_established",
                "data": {
                    "client_id": client_id,
                    "server_time": datetime.utcnow().isoformat(),
                    "heartbeat_interval": self.settings.WS_HEARTBEAT_INTERVAL
                }
            })
            
            logger.info("WebSocket connected", client_id=client_id)
            
        except Exception as e:
            logger.error("WebSocket connection failed", client_id=client_id, error=str(e))
            await websocket.close(code=1011, reason="Connection error")
    
    async def disconnect(self, client_id: str) -> None:
        """Handle WebSocket disconnection"""
        try:
            if client_id in self.active_connections:
                # Remove from all rooms
                if client_id in self.client_subscriptions:
                    for room in self.client_subscriptions[client_id].copy():
                        await self.leave_room(client_id, room)
                
                # Clean up
                del self.active_connections[client_id]
                if client_id in self.client_subscriptions:
                    del self.client_subscriptions[client_id]
                
                logger.info("WebSocket disconnected", client_id=client_id)
                
        except Exception as e:
            logger.error("WebSocket disconnect error", client_id=client_id, error=str(e))
    
    async def send_personal_message(self, client_id: str, message: Dict[str, Any]) -> bool:
        """Send message to specific client"""
        try:
            if client_id not in self.active_connections:
                return False
            
            connection = self.active_connections[client_id]
            websocket_message = WebSocketMessage(
                type=message.get("type", "message"),
                data=message.get("data", {}),
                timestamp=datetime.utcnow(),
                client_id=client_id
            )
            
            await connection.websocket.send_text(websocket_message.json())
            return True
            
        except WebSocketDisconnect:
            await self.disconnect(client_id)
            return False
        except Exception as e:
            logger.error("Send personal message failed", client_id=client_id, error=str(e))
            return False
    
    async def broadcast_to_room(self, room: str, message: Dict[str, Any], exclude: Optional[str] = None) -> int:
        """Broadcast message to all clients in room"""
        sent_count = 0
        
        if room not in self.room_subscriptions:
            return sent_count
        
        clients = self.room_subscriptions[room].copy()
        
        for client_id in clients:
            if exclude and client_id == exclude:
                continue
            
            success = await self.send_personal_message(client_id, message)
            if success:
                sent_count += 1
        
        return sent_count
    
    async def broadcast_to_all(self, message: Dict[str, Any], exclude: Optional[str] = None) -> int:
        """Broadcast message to all connected clients"""
        sent_count = 0
        
        for client_id in list(self.active_connections.keys()):
            if exclude and client_id == exclude:
                continue
            
            success = await self.send_personal_message(client_id, message)
            if success:
                sent_count += 1
        
        return sent_count
    
    async def join_room(self, client_id: str, room: str) -> bool:
        """Add client to room"""
        try:
            if client_id not in self.active_connections:
                return False
            
            # Add to room
            if room not in self.room_subscriptions:
                self.room_subscriptions[room] = set()
            self.room_subscriptions[room].add(client_id)
            
            # Add to client subscriptions
            self.client_subscriptions[client_id].add(room)
            
            # Update connection info
            self.active_connections[client_id].subscriptions.add(room)
            
            logger.info("Client joined room", client_id=client_id, room=room)
            return True
            
        except Exception as e:
            logger.error("Join room failed", client_id=client_id, room=room, error=str(e))
            return False
    
    async def leave_room(self, client_id: str, room: str) -> bool:
        """Remove client from room"""
        try:
            # Remove from room
            if room in self.room_subscriptions:
                self.room_subscriptions[room].discard(client_id)
                if not self.room_subscriptions[room]:
                    del self.room_subscriptions[room]
            
            # Remove from client subscriptions
            if client_id in self.client_subscriptions:
                self.client_subscriptions[client_id].discard(room)
            
            # Update connection info
            if client_id in self.active_connections:
                self.active_connections[client_id].subscriptions.discard(room)
            
            logger.info("Client left room", client_id=client_id, room=room)
            return True
            
        except Exception as e:
            logger.error("Leave room failed", client_id=client_id, room=room, error=str(e))
            return False
    
    async def handle_message(self, client_id: str, message: str) -> None:
        """Handle incoming WebSocket message"""
        try:
            data = json.loads(message)
            message_obj = WebSocketMessage(**data)
            
            # Update last ping
            if client_id in self.active_connections:
                self.active_connections[client_id].last_ping = datetime.utcnow()
            
            # Handle different message types
            if message_obj.type == "ping":
                await self.send_personal_message(client_id, {
                    "type": "pong",
                    "data": {"timestamp": datetime.utcnow().isoformat()}
                })
            
            elif message_obj.type == "join_room":
                room = message_obj.data.get("room")
                if room:
                    await self.join_room(client_id, room)
            
            elif message_obj.type == "leave_room":
                room = message_obj.data.get("room")
                if room:
                    await self.leave_room(client_id, room)
            
            elif message_obj.type == "update_location":
                await self.update_client_location(client_id, message_obj.data)
            
            elif message_obj.type == "subscribe_bus":
                bus_id = message_obj.data.get("bus_id")
                if bus_id:
                    await self.join_room(client_id, f"bus_{bus_id}")
            
            elif message_obj.type == "subscribe_route":
                route_id = message_obj.data.get("route_id")
                if route_id:
                    await self.join_room(client_id, f"route_{route_id}")
            
            else:
                logger.warning("Unknown message type", type=message_obj.type, client_id=client_id)
                
        except (json.JSONDecodeError, ValidationError) as e:
            logger.error("Invalid message format", client_id=client_id, error=str(e))
            await self.send_personal_message(client_id, {
                "type": "error",
                "data": {"message": "Invalid message format"}
            })
        except Exception as e:
            logger.error("Message handling failed", client_id=client_id, error=str(e))
    
    async def update_client_location(self, client_id: str, location_data: Dict[str, Any]) -> None:
        """Update client location"""
        try:
            if client_id in self.active_connections:
                self.active_connections[client_id].location = {
                    "latitude": location_data.get("latitude"),
                    "longitude": location_data.get("longitude"),
                    "accuracy": location_data.get("accuracy"),
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                # Cache location in Redis
                cache = await get_cache()
                await cache.set(
                    f"client_location:{client_id}",
                    self.active_connections[client_id].location,
                    ttl=300  # 5 minutes
                )
                
        except Exception as e:
            logger.error("Update location failed", client_id=client_id, error=str(e))
    
    async def start_heartbeat(self) -> None:
        """Start heartbeat monitoring"""
        if self.heartbeat_task:
            return
        
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        logger.info("Heartbeat monitoring started")
    
    async def _heartbeat_loop(self) -> None:
        """Heartbeat monitoring loop"""
        while True:
            try:
                await asyncio.sleep(self.settings.WS_HEARTBEAT_INTERVAL)
                
                current_time = datetime.utcnow()
                timeout_threshold = current_time - timedelta(
                    seconds=self.settings.WS_HEARTBEAT_INTERVAL * 2
                )
                
                # Check for stale connections
                stale_clients = []
                for client_id, connection in self.active_connections.items():
                    if connection.last_ping < timeout_threshold:
                        stale_clients.append(client_id)
                
                # Disconnect stale clients
                for client_id in stale_clients:
                    logger.info("Disconnecting stale client", client_id=client_id)
                    await self.disconnect(client_id)
                
            except Exception as e:
                logger.error("Heartbeat loop error", error=str(e))
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        return {
            "total_connections": len(self.active_connections),
            "total_rooms": len(self.room_subscriptions),
            "connections_by_room": {
                room: len(clients) for room, clients in self.room_subscriptions.items()
            },
            "average_subscriptions": (
                sum(len(subs) for subs in self.client_subscriptions.values()) / 
                len(self.client_subscriptions) if self.client_subscriptions else 0
            )
        }
    
    async def cleanup(self) -> None:
        """Cleanup resources"""
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
        
        # Disconnect all clients
        for client_id in list(self.active_connections.keys()):
            await self.disconnect(client_id)
        
        logger.info("WebSocket manager cleanup completed")
