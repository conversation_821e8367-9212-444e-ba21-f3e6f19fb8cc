from flask import Blueprint, request, jsonify
from ..models.passenger import PassengerInfo, SeatReservation
from ..models.bus import Bus
from ..models.stop import BusStop
from datetime import datetime, timedelta
import uuid

passengers_bp = Blueprint('passengers', __name__)

@passengers_bp.route('/register', methods=['POST'])
def register_passenger():
    """Register a new passenger"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400
        
        # Generate passenger ID if not provided
        if 'passenger_id' not in data:
            data['passenger_id'] = str(uuid.uuid4())[:8]
        
        passenger = PassengerInfo(**data)
        passenger.save()
        
        return jsonify({
            'success': True,
            'data': passenger.to_dict(),
            'message': 'Passenger registered successfully'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@passengers_bp.route('/<passenger_id>', methods=['GET'])
def get_passenger(passenger_id):
    """Get passenger information"""
    try:
        passenger = PassengerInfo.query.filter_by(
            passenger_id=passenger_id,
            is_active=True
        ).first()
        
        if not passenger:
            return jsonify({'success': False, 'error': 'Passenger not found'}), 404
        
        return jsonify({
            'success': True,
            'data': passenger.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@passengers_bp.route('/reservations', methods=['POST'])
def create_reservation():
    """Create a new seat reservation"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['passenger_id', 'bus_id', 'route_id', 'boarding_stop_id', 'alighting_stop_id']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400
        
        # Validate entities exist
        passenger = PassengerInfo.query.filter_by(passenger_id=data['passenger_id']).first()
        if not passenger:
            return jsonify({'success': False, 'error': 'Passenger not found'}), 404
        
        bus = Bus.get_by_id(data['bus_id'])
        if not bus:
            return jsonify({'success': False, 'error': 'Bus not found'}), 404
        
        # Check seat availability
        available_seats = bus.get_available_seats()
        if available_seats <= 0:
            return jsonify({'success': False, 'error': 'No seats available'}), 400
        
        # Generate reservation code
        reservation_code = f"RES-{datetime.now().strftime('%Y%m%d%H%M%S')}-{uuid.uuid4().hex[:6].upper()}"
        
        # Calculate fare (simplified)
        from ..models.route import Route
        route = Route.get_by_id(data['route_id'])
        fare_amount = route.base_fare if route else 15.0
        
        reservation = SeatReservation(
            reservation_code=reservation_code,
            passenger_id=passenger.id,
            bus_id=data['bus_id'],
            route_id=data['route_id'],
            boarding_stop_id=data['boarding_stop_id'],
            alighting_stop_id=data['alighting_stop_id'],
            scheduled_boarding_time=datetime.fromisoformat(data['scheduled_boarding_time']),
            fare_amount=fare_amount,
            seat_number=data.get('seat_number'),
            requires_assistance=data.get('requires_assistance', False),
            special_notes=data.get('special_notes')
        )
        
        reservation.save()
        
        return jsonify({
            'success': True,
            'data': reservation.to_dict(),
            'message': 'Reservation created successfully'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@passengers_bp.route('/reservations/<reservation_code>', methods=['GET'])
def get_reservation(reservation_code):
    """Get reservation details"""
    try:
        reservation = SeatReservation.query.filter_by(
            reservation_code=reservation_code,
            is_active=True
        ).first()
        
        if not reservation:
            return jsonify({'success': False, 'error': 'Reservation not found'}), 404
        
        return jsonify({
            'success': True,
            'data': reservation.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@passengers_bp.route('/reservations/<reservation_code>/cancel', methods=['POST'])
def cancel_reservation(reservation_code):
    """Cancel a reservation"""
    try:
        reservation = SeatReservation.query.filter_by(
            reservation_code=reservation_code,
            is_active=True
        ).first()
        
        if not reservation:
            return jsonify({'success': False, 'error': 'Reservation not found'}), 404
        
        if not reservation.can_be_cancelled():
            return jsonify({'success': False, 'error': 'Reservation cannot be cancelled'}), 400
        
        reservation.status = 'CANCELLED'
        reservation.save()
        
        return jsonify({
            'success': True,
            'message': 'Reservation cancelled successfully'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@passengers_bp.route('/<passenger_id>/reservations', methods=['GET'])
def get_passenger_reservations(passenger_id):
    """Get all reservations for a passenger"""
    try:
        passenger = PassengerInfo.query.filter_by(
            passenger_id=passenger_id,
            is_active=True
        ).first()
        
        if not passenger:
            return jsonify({'success': False, 'error': 'Passenger not found'}), 404
        
        reservations = SeatReservation.query.filter_by(
            passenger_id=passenger.id,
            is_active=True
        ).order_by(SeatReservation.created_at.desc()).all()
        
        return jsonify({
            'success': True,
            'data': [reservation.to_dict() for reservation in reservations],
            'count': len(reservations)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
