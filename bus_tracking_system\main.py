"""
Advanced GPS-Based Bus Tracking System
Modern FastAPI microservices architecture with real-time analytics
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from prometheus_client import make_asgi_app
import structlog

from backend.core.config import get_settings
from backend.core.database import init_db, close_db
from backend.core.redis_client import init_redis, close_redis
from backend.core.monitoring import setup_monitoring
from backend.api.v1 import api_router
from backend.services.websocket_manager import WebSocketManager
from backend.services.analytics_service import AnalyticsService
from backend.services.ml_service import MLPredictionService
from backend.middleware.auth import AuthMiddleware
from backend.middleware.rate_limit import RateLimitMiddleware
from backend.middleware.logging import LoggingMiddleware

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Global services
websocket_manager = WebSocketManager()
analytics_service = AnalyticsService()
ml_service = MLPredictionService()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("Starting Advanced Bus Tracking System...")
    
    # Initialize services
    await init_db()
    await init_redis()
    await setup_monitoring()
    await analytics_service.initialize()
    await ml_service.initialize()
    
    # Start background tasks
    asyncio.create_task(websocket_manager.start_heartbeat())
    asyncio.create_task(analytics_service.start_real_time_processing())
    asyncio.create_task(ml_service.start_prediction_engine())
    
    logger.info("System initialized successfully")
    
    yield
    
    # Cleanup
    logger.info("Shutting down system...")
    await close_redis()
    await close_db()
    await analytics_service.cleanup()
    await ml_service.cleanup()
    logger.info("System shutdown complete")


def create_application() -> FastAPI:
    """Create and configure FastAPI application"""
    settings = get_settings()
    
    app = FastAPI(
        title="Advanced GPS Bus Tracking System",
        description="Modern microservices-based bus tracking with AI/ML capabilities",
        version="2.0.0",
        docs_url="/api/docs",
        redoc_url="/api/redoc",
        openapi_url="/api/openapi.json",
        lifespan=lifespan
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    app.add_middleware(AuthMiddleware)
    app.add_middleware(RateLimitMiddleware)
    app.add_middleware(LoggingMiddleware)
    
    # Include routers
    app.include_router(api_router, prefix="/api/v1")
    
    # Add Prometheus metrics endpoint
    metrics_app = make_asgi_app()
    app.mount("/metrics", metrics_app)
    
    # WebSocket endpoint
    @app.websocket("/ws/{client_id}")
    async def websocket_endpoint(websocket, client_id: str):
        await websocket_manager.connect(websocket, client_id)
        try:
            while True:
                data = await websocket.receive_text()
                await websocket_manager.handle_message(client_id, data)
        except Exception as e:
            logger.error("WebSocket error", error=str(e), client_id=client_id)
        finally:
            await websocket_manager.disconnect(client_id)
    
    # Health check endpoints
    @app.get("/health")
    async def health_check():
        """Basic health check"""
        return {"status": "healthy", "service": "bus-tracking-system"}
    
    @app.get("/health/detailed")
    async def detailed_health_check():
        """Detailed health check with service status"""
        try:
            # Check database
            db_status = await check_database_health()
            
            # Check Redis
            redis_status = await check_redis_health()
            
            # Check ML service
            ml_status = await ml_service.health_check()
            
            return {
                "status": "healthy",
                "services": {
                    "database": db_status,
                    "redis": redis_status,
                    "ml_service": ml_status,
                    "websocket_connections": len(websocket_manager.active_connections)
                }
            }
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            raise HTTPException(status_code=503, detail="Service unhealthy")
    
    # Global exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        logger.error("Unhandled exception", error=str(exc), path=request.url.path)
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"}
        )
    
    return app


async def check_database_health() -> Dict[str, Any]:
    """Check database connectivity"""
    try:
        from backend.core.database import get_db_session
        async with get_db_session() as session:
            await session.execute("SELECT 1")
        return {"status": "healthy"}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}


async def check_redis_health() -> Dict[str, Any]:
    """Check Redis connectivity"""
    try:
        from backend.core.redis_client import get_redis
        redis = await get_redis()
        await redis.ping()
        return {"status": "healthy"}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}


# Create the application instance
app = create_application()

if __name__ == "__main__":
    settings = get_settings()
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_config=None,  # Use structlog instead
        access_log=False,  # Handled by middleware
    )
