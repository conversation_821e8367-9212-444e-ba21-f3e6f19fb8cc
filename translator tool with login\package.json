{"name": "advanced-translator-platform", "version": "2.0.0", "description": "Advanced AI-powered translation platform with modern features", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@mui/lab": "^5.0.0-alpha.156", "axios": "^1.6.2", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "socket.io-client": "^4.7.4", "react-dropzone": "^14.2.3", "react-speech-kit": "^3.0.1", "react-i18next": "^13.5.0", "i18next": "^23.7.6", "i18next-browser-languagedetector": "^7.2.0", "react-helmet-async": "^1.3.0", "framer-motion": "^10.16.16", "recharts": "^2.8.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "@types/lodash": "^4.14.202", "react-hook-form": "^7.48.2", "yup": "^1.4.0", "@hookform/resolvers": "^3.3.2", "react-hot-toast": "^2.4.1", "react-syntax-highlighter": "^15.5.0", "@types/react-syntax-highlighter": "^15.5.11", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "react-pdf": "^7.6.0", "mammoth": "^1.6.0", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}", "type-check": "tsc --noEmit", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-pdf": "^7.0.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "bundle-analyzer": "^0.1.0"}, "proxy": "http://localhost:8001"}