/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #2c3e50, #3498db);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo i {
    font-size: 2rem;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.nav-link:hover,
.nav-link.active {
    background-color: rgba(255,255,255,0.2);
}

/* Main Content */
.main {
    padding: 2rem 0;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

/* Hero Section */
.hero {
    text-align: center;
    margin-bottom: 3rem;
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.hero p {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Search Section */
.search-section {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.search-box {
    display: flex;
    background: white;
    border-radius: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    min-width: 300px;
}

.search-box input {
    flex: 1;
    padding: 12px 20px;
    border: none;
    outline: none;
    font-size: 1rem;
}

.search-box button {
    background: #3498db;
    color: white;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-box button:hover {
    background: #2980b9;
}

.location-btn button {
    background: #27ae60;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

.location-btn button:hover {
    background: #229954;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Cards */
.card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card h3 i {
    color: #3498db;
}

/* Map */
.map-container {
    height: 300px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.map-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

/* Lists */
.arrivals-list,
.routes-list,
.alerts-list {
    max-height: 300px;
    overflow-y: auto;
}

.arrival-item,
.route-item,
.alert-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.arrival-item:last-child,
.route-item:last-child,
.alert-item:last-child {
    border-bottom: none;
}

.arrival-time {
    font-weight: bold;
    color: #27ae60;
}

.route-number {
    background: #3498db;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
}

.alert-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.alert-info {
    background: #3498db;
    color: white;
}

.alert-warning {
    background: #f39c12;
    color: white;
}

.alert-emergency {
    background: #e74c3c;
    color: white;
}

/* Loading */
.loading {
    text-align: center;
    color: #666;
    padding: 2rem;
}

/* Filters */
.routes-filter,
.stops-filter,
.alerts-filter {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.routes-filter input,
.stops-filter input,
.alerts-filter select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

/* Grids */
.routes-grid,
.stops-grid,
.alerts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 20px;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    position: relative;
}

.close {
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
}

.close:hover {
    color: #000;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav {
        gap: 1rem;
    }

    .hero h2 {
        font-size: 2rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .search-section {
        flex-direction: column;
    }

    .search-box {
        min-width: 100%;
    }
}
