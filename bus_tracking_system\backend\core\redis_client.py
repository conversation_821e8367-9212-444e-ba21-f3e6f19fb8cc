"""
Advanced Redis client for caching and real-time features
"""

import json
import asyncio
from typing import Any, Optional, Dict, List, Union
from contextlib import asynccontextmanager
import structlog

import redis.asyncio as redis
from redis.asyncio import ConnectionPool, Redis
from redis.exceptions import RedisError, ConnectionError

from backend.core.config import get_settings

logger = structlog.get_logger()

# Global Redis instances
redis_client: Optional[Redis] = None
redis_pool: Optional[ConnectionPool] = None


async def init_redis() -> None:
    """Initialize Redis connection pool"""
    global redis_client, redis_pool
    
    settings = get_settings()
    
    try:
        # Create connection pool
        redis_pool = ConnectionPool.from_url(
            settings.REDIS_URL,
            max_connections=settings.REDIS_POOL_SIZE,
            socket_timeout=settings.REDIS_TIMEOUT,
            socket_connect_timeout=settings.REDIS_TIMEOUT,
            retry_on_timeout=True,
            health_check_interval=30
        )
        
        # Create Redis client
        redis_client = Redis(connection_pool=redis_pool, decode_responses=True)
        
        # Test connection
        await redis_client.ping()
        
        logger.info("Redis initialized successfully")
        
    except Exception as e:
        logger.error("Failed to initialize Redis", error=str(e))
        raise


async def close_redis() -> None:
    """Close Redis connections"""
    global redis_client, redis_pool
    
    if redis_client:
        await redis_client.close()
    
    if redis_pool:
        await redis_pool.disconnect()
    
    logger.info("Redis connections closed")


async def get_redis() -> Redis:
    """Get Redis client instance"""
    if not redis_client:
        raise RuntimeError("Redis not initialized")
    return redis_client


class RedisCache:
    """Advanced Redis caching utilities"""
    
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
        self.default_ttl = 3600  # 1 hour
    
    async def get(self, key: str, default: Any = None) -> Any:
        """Get value from cache with JSON deserialization"""
        try:
            value = await self.redis.get(key)
            if value is None:
                return default
            return json.loads(value)
        except (RedisError, json.JSONDecodeError) as e:
            logger.error("Cache get failed", key=key, error=str(e))
            return default
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache with JSON serialization"""
        try:
            serialized_value = json.dumps(value, default=str)
            ttl = ttl or self.default_ttl
            await self.redis.setex(key, ttl, serialized_value)
            return True
        except (RedisError, json.JSONEncodeError) as e:
            logger.error("Cache set failed", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        try:
            result = await self.redis.delete(key)
            return bool(result)
        except RedisError as e:
            logger.error("Cache delete failed", key=key, error=str(e))
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            result = await self.redis.exists(key)
            return bool(result)
        except RedisError as e:
            logger.error("Cache exists check failed", key=key, error=str(e))
            return False
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment counter in cache"""
        try:
            result = await self.redis.incrby(key, amount)
            return result
        except RedisError as e:
            logger.error("Cache increment failed", key=key, error=str(e))
            return None
    
    async def expire(self, key: str, ttl: int) -> bool:
        """Set expiration for key"""
        try:
            result = await self.redis.expire(key, ttl)
            return bool(result)
        except RedisError as e:
            logger.error("Cache expire failed", key=key, error=str(e))
            return False
    
    async def get_many(self, keys: List[str]) -> Dict[str, Any]:
        """Get multiple values from cache"""
        try:
            values = await self.redis.mget(keys)
            result = {}
            for key, value in zip(keys, values):
                if value is not None:
                    try:
                        result[key] = json.loads(value)
                    except json.JSONDecodeError:
                        result[key] = value
            return result
        except RedisError as e:
            logger.error("Cache get_many failed", keys=keys, error=str(e))
            return {}
    
    async def set_many(self, mapping: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Set multiple values in cache"""
        try:
            pipe = self.redis.pipeline()
            ttl = ttl or self.default_ttl
            
            for key, value in mapping.items():
                serialized_value = json.dumps(value, default=str)
                pipe.setex(key, ttl, serialized_value)
            
            await pipe.execute()
            return True
        except (RedisError, json.JSONEncodeError) as e:
            logger.error("Cache set_many failed", error=str(e))
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern"""
        try:
            keys = await self.redis.keys(pattern)
            if keys:
                result = await self.redis.delete(*keys)
                return result
            return 0
        except RedisError as e:
            logger.error("Cache clear_pattern failed", pattern=pattern, error=str(e))
            return 0


class RedisPubSub:
    """Redis Pub/Sub for real-time messaging"""
    
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
        self.pubsub = None
    
    async def subscribe(self, channels: Union[str, List[str]]) -> None:
        """Subscribe to channels"""
        try:
            self.pubsub = self.redis.pubsub()
            if isinstance(channels, str):
                channels = [channels]
            await self.pubsub.subscribe(*channels)
            logger.info("Subscribed to channels", channels=channels)
        except RedisError as e:
            logger.error("Pub/Sub subscribe failed", channels=channels, error=str(e))
            raise
    
    async def unsubscribe(self, channels: Union[str, List[str]] = None) -> None:
        """Unsubscribe from channels"""
        try:
            if self.pubsub:
                if channels:
                    if isinstance(channels, str):
                        channels = [channels]
                    await self.pubsub.unsubscribe(*channels)
                else:
                    await self.pubsub.unsubscribe()
                logger.info("Unsubscribed from channels", channels=channels)
        except RedisError as e:
            logger.error("Pub/Sub unsubscribe failed", error=str(e))
    
    async def publish(self, channel: str, message: Any) -> int:
        """Publish message to channel"""
        try:
            serialized_message = json.dumps(message, default=str)
            result = await self.redis.publish(channel, serialized_message)
            return result
        except (RedisError, json.JSONEncodeError) as e:
            logger.error("Pub/Sub publish failed", channel=channel, error=str(e))
            return 0
    
    async def listen(self):
        """Listen for messages"""
        if not self.pubsub:
            raise RuntimeError("Not subscribed to any channels")
        
        try:
            async for message in self.pubsub.listen():
                if message['type'] == 'message':
                    try:
                        data = json.loads(message['data'])
                        yield {
                            'channel': message['channel'],
                            'data': data
                        }
                    except json.JSONDecodeError:
                        yield {
                            'channel': message['channel'],
                            'data': message['data']
                        }
        except RedisError as e:
            logger.error("Pub/Sub listen failed", error=str(e))
    
    async def close(self) -> None:
        """Close pub/sub connection"""
        if self.pubsub:
            await self.pubsub.close()


class RedisRateLimiter:
    """Redis-based rate limiter"""
    
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
    
    async def is_allowed(self, key: str, limit: int, window: int) -> bool:
        """Check if request is allowed under rate limit"""
        try:
            pipe = self.redis.pipeline()
            pipe.incr(key)
            pipe.expire(key, window)
            results = await pipe.execute()
            
            current_count = results[0]
            return current_count <= limit
            
        except RedisError as e:
            logger.error("Rate limiter check failed", key=key, error=str(e))
            return True  # Allow on error
    
    async def get_remaining(self, key: str, limit: int) -> int:
        """Get remaining requests in current window"""
        try:
            current = await self.redis.get(key)
            if current is None:
                return limit
            return max(0, limit - int(current))
        except RedisError as e:
            logger.error("Rate limiter remaining check failed", key=key, error=str(e))
            return limit


# Global instances
cache: Optional[RedisCache] = None
pubsub: Optional[RedisPubSub] = None
rate_limiter: Optional[RedisRateLimiter] = None


async def init_redis_services():
    """Initialize Redis-based services"""
    global cache, pubsub, rate_limiter
    
    redis_client = await get_redis()
    cache = RedisCache(redis_client)
    pubsub = RedisPubSub(redis_client)
    rate_limiter = RedisRateLimiter(redis_client)
    
    logger.info("Redis services initialized")


# Utility functions
async def get_cache() -> RedisCache:
    """Get cache instance"""
    if not cache:
        await init_redis_services()
    return cache


async def get_pubsub() -> RedisPubSub:
    """Get pub/sub instance"""
    if not pubsub:
        await init_redis_services()
    return pubsub


async def get_rate_limiter() -> RedisRateLimiter:
    """Get rate limiter instance"""
    if not rate_limiter:
        await init_redis_services()
    return rate_limiter
