from flask import Blueprint, request, jsonify
from ..models.route import Route, RouteStop
from ..models.stop import BusStop

routes_bp = Blueprint('routes', __name__)

@routes_bp.route('/', methods=['GET'])
def get_all_routes():
    """Get all active routes"""
    try:
        routes = Route.get_all_active()
        return jsonify({
            'success': True,
            'data': [route.to_dict() for route in routes],
            'count': len(routes)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@routes_bp.route('/<route_id>', methods=['GET'])
def get_route(route_id):
    """Get specific route details"""
    try:
        route = Route.get_by_id(route_id)
        if not route:
            return jsonify({'success': False, 'error': 'Route not found'}), 404
        
        return jsonify({
            'success': True,
            'data': route.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@routes_bp.route('/<route_id>/stops', methods=['GET'])
def get_route_stops(route_id):
    """Get all stops for a specific route in order"""
    try:
        route = Route.get_by_id(route_id)
        if not route:
            return jsonify({'success': False, 'error': 'Route not found'}), 404
        
        stops = route.get_stops_in_order()
        return jsonify({
            'success': True,
            'data': [stop.to_dict() for stop in stops],
            'count': len(stops)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@routes_bp.route('/<route_id>/schedule', methods=['GET'])
def get_route_schedule(route_id):
    """Get schedule information for a route"""
    try:
        route = Route.get_by_id(route_id)
        if not route:
            return jsonify({'success': False, 'error': 'Route not found'}), 404
        
        next_buses = route.get_next_bus_times()
        
        return jsonify({
            'success': True,
            'data': {
                'route_number': route.route_number,
                'route_name': route.route_name,
                'first_bus': route.first_bus_time.strftime('%H:%M'),
                'last_bus': route.last_bus_time.strftime('%H:%M'),
                'frequency_minutes': route.frequency_minutes,
                'next_buses': [time.strftime('%H:%M') for time in next_buses]
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@routes_bp.route('/search', methods=['GET'])
def search_routes():
    """Search routes by name or number"""
    try:
        query = request.args.get('q', '').strip()
        if not query:
            return jsonify({'success': False, 'error': 'Search query required'}), 400
        
        routes = Route.query.filter(
            (Route.route_number.ilike(f'%{query}%')) |
            (Route.route_name.ilike(f'%{query}%')) |
            (Route.start_location.ilike(f'%{query}%')) |
            (Route.end_location.ilike(f'%{query}%')),
            Route.is_active == True
        ).all()
        
        return jsonify({
            'success': True,
            'data': [route.to_dict() for route in routes],
            'count': len(routes)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
