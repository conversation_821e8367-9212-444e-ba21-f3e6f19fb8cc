<!DOCTYPE html> 
<html lang="en"> 
<head> 
<meta charset="UTF-8"> 
<meta name="viewport" content="width=device-width, initial-scale=1.0"> 
<title>Register</title> 
<link rel="stylesheet" href="stylesb1.css"> 
<link 	href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'> 
</head> 
<body> 
<div class="wrapper"> 
<form action=""> 
<h1>Registeration</h1> 
<div class="input-box"> 
<input type="text" placeholder="Enter your Username" required> <i class='bx bxs-user'></i> 
</div> 
<div class="input-box"> 
<input type="text" placeholder="Enter your Usermail" required> <i class='bx bxs-user'></i> 
</div> 
<div class="input-box"> 
<input type="password" placeholder="Enter your Password" required> <i class='bx bxs-lock-alt' ></i> 
</div> 
<div class="input-box"> 
 
<input type="password" placeholder="Enter your conform Password" required> 
<i class='bx bxs-lock-alt' ></i> 
</div> 
<div class="remember-forgot"> 
<label><input type="checkbox">Remember Me</label> 
<a href="#">Forgot Password</a> 
</div> 
 
 
<button type="submit" class="btn"><a href="login.html">Register</button></a> 
  
<div class="register-link"> 
<p>Dont have an account? <a href="login.html">Login</a></p> </div> 
</form> 
</div> 
</body> 
</html> 
 
 
LOGIN CODE: 
 
 
<!DOCTYPE html> 
<html lang="en"> 
<head> 
<meta charset="UTF-8"> 
<meta name="viewport" content="width=device-width, initial-scale=1.0"> 
<title>Translator</title> 
<link rel="stylesheet" href="stylesb.css"> 
<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'> 
</head> 
<body> 
<div class="wrapper"> 
<form action=""> <h1>Login</h1> 
<div class="input-box"> 
<input type="text" placeholder="Username" required> 
<i class='bx bxs-user'></i> 
</div> 
<div class="input-box"> 
<input type="password" placeholder="Password" required> 
<i class='bx bxs-lock-alt' ></i> 
</div> 
<div class="remember-forgot"> 
<label><input type="checkbox">Remember Me</label> 
<a href="#">Forgot Password</a> 
</div> 
 
 
<button type="submit" class="btn"><a href="index.html">Login</button></a> 
  
<div class="register-link"> 
<p>Dont have an account? <a href="registerb.html">Register</a></p> </div> 
</form> 
</div> 
</body> 
</html> 
 
 
 
 
 
HOME PAGE CODE: 
<!DOCTYPE html> 
<html lang="en" dir="ltr"> 
<head> 
<meta charset="utf-8"> 
<title>TRANSLATOR TOOL|| TNcpl11</title> 
<link rel="stylesheet" href="style.css"> 
<meta name="viewport" content="width=device-width, initial-scale=1.0"> 
<!-- Font Awesome CDN Link for Icons --> 
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font- awesome/5.15.3/css/all.min.css" /> 
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.min.js"></script> 
</head> 
 
 
<body> 
<div class="heading1"> 
<p><b>Translator</b><br><b>Tools</b></p> 
 
 
</div> 
<br> 
<div class="container"> 
<div class="wrapper"> 
<div class="text-input"> 
<textarea spellcheck="false" class="from-text" placeholder="Enter text" id="input"></textarea> 
<textarea spellcheck="false" readonly disabled class="to-text" placeholder="Translation"></textarea> 
</div> 
<ul class="controls"> 
<li class="row from"> 
<div class="icons"> 
<i id="from" class="fas fa-volume-up"></i> 
<i id="from" class="fas fa-copy"></i> 
<i id="from" class="fa fa-file"></i> 
</div> 
<select></select> 
</li> 
<li class="exchange"><i class="fas fa-exchange-alt"></i></li> 
<li class="row to"> 
<select></select> 
<div class="icons"> 
<i id="to" class="fas fa-volume-up"></i> 
<i id="to" class="fas fa-copy"></i> 
</div> 
</li> 
</ul> 
</div> 
<form> 
<input type="file" id="pdfInput" accept=".pdf"> 
</form> 
<pre id="pdfText"></pre> 
<button>Translate Text</button> 
</div> 
 
 
<script src="js/countries.js"></script> 
<script src="js/script.js"></script> 
<script src="js/upload.js"></script> 
</body> 
</html> 
 
 
LANGUAGE AND TRANSLATING CODE: 
/* Import Google Font - Poppins */ 
@import 
url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&displ ay=swap'); 
*{ margin: 0; padding: 0; box-sizing: border-box; font-family: 'Poppins', sans-serif; 
} 
body{ 
display: flex; 
align-items: 	center; justify-content: center; padding: 0 10px; min-height: 100vh; background-image: url('5.jpg'); background-repeat: no-repeat; background-size: 100% 100%; 
} p{ 
color: white; padding-right: 40px; font-size: 70px; text-align: center; text-shadow: 1px 7px grey; 
} 
.container{ 
	max-width: 	690px; 
width: 	100%; padding: 	30px; background: 	#fff; border-radius: 7px; box-shadow: 0 10px 20px rgba(0,0,0,0.01); 
} 
.wrapper{ border-radius: 5px; border: 1px solid #ccc; 
} 
.wrapper .text-input{ display: flex; border-bottom: 1px solid #ccc; 
} 
.text-input .to-text{ 
border-radius: 0px; border-left: 1px solid #ccc; 
} 
.text-input textarea{ 
height: 	250px; 
width: 	100%; 
border: 	none; 
outline: 	none; 
resize: 	none; 
background: none; font-size: 18px; padding: 10px 15px; border-radius: 5px; 
} 
.text-input textarea::placeholder{ color: #b7b6b6; 
} 
.controls, li, .icons, .icons i{ display: flex; align-items: center; justify-content: space-between; 
} 
.controls{ list-style: none; padding: 12px 15px; 
} 
.controls .row .icons{ width: 38%; 
} 
.controls .row .icons i{ width: 	50px; 
color: #adadad; font-size: 14px; cursor: pointer; transition: transform 0.2s ease; justify-content: center; 
} 
.controls .row.from .icons{ padding-right: 15px; border-right: 1px solid #ccc; 
} 
.controls .row.to .icons{ padding-left: 15px; border-left: 1px solid #ccc; 
} 
.controls .row select{ color: 	#333; border: 	none; outline: 	none; font-size: 18px; background: none; padding-left: 5px; 
} 
.text-input textarea::-webkit-scrollbar{ width: 4px; 
} 
.controls .row select::-webkit-scrollbar{ width: 8px; 
} 
.text-input textarea::-webkit-scrollbar-track, 
.controls .row select::-webkit-scrollbar-track{ background: #fff; 
} 
.text-input textarea::-webkit-scrollbar-thumb{ background: #ddd; border-radius: 8px; 
} 
.controls .row select::-webkit-scrollbar-thumb{ 
background: #999; border-radius: 8px; border-right: 2px solid #ffffff; 
} 
.controls .exchange{ color: #adadad; cursor: pointer; font-size: 16px; transition: transform 0.2s ease; 
} 
.controls i:active{ 
transform: scale(0.9); 
} 
.container button{ width: 	100%; padding: 	14px; 
outline: none; border: 2px solid black; color:black; cursor: pointer; 	margin- top: 20px; font-size: 
	17px; 	 border- 
radius: 5px; background: transparent; 
} 
.container button:hover{ color: white; border-radius: 100px; border:2px solid white; background-color: black; 
} 
  
@media (max-width: 660px){ 
.container{ padding: 20px; 
} 
.wrapper .text-input{ flex-direction: column; 
} 
.text-input .to-text{ border-left: 0px; border-top: 1px solid #ccc; 
} 
.text-input textarea{ 
height: 200px; 
} 
.controls .row .icons{ display: none; 
} 
.container button{ padding: 13px; font-size: 16px; 
} 
.controls .row select{ font-size: 16px; 
} 
.controls .exchange{ font-size: 14px; 
} 
} 
 
 
*{ 
margin: 0; padding: 0; box-sizing: border-box; font-family: "Poppins", sans-serif; } 
body{ 
display: flex; justify-content: center; align-items: 	center; min-height: 100vh; background: url(5.jpg) no-repeat; background-size: cover; background-position: center; 
} a{ text-decoration: none; 
} 
.wrapper{ width: 420px; background: transparent; border: 2px solid rgba(255, 255, 255, .2); backdrop-filter: blur(4px); color: #fff; border-radius: 12px; padding: 30px 40px; 
} 
.wrapper h1{ font-size: 36px; text-align: center; 
} 
.wrapper .input-box{ position: 	relative; width: 	100%; 
height: 50px; 
 
  
margin: 30px 0; 
} 
.input-box input{ width: 	100%; height: 100%; background: transparent; border: none; outline: none; 
border: 2px solid rgba(255, 255, 255, .2); border-radius: 40px; font-size: 16px; color: #fff; padding: 20px 45px 20px 20px; 
} 
.input-box input::placeholder{ color: #fff; 
} 
.input-box 	i{ position: absolute; right: 20px; top: 30%; transform: translate(-50%); font-size: 20px; 
 
} 
.wrapper .remember-forgot{ display: flex; justify-content: space-between; font-size: 14.5px; 
margin: -15px 0 15px; 
} 
.remember-forgot label input{ accent-color: #fff; 
margin-right: 3px; 
  
} 
.remember-forgot a{ color: #fff; 
text-decoration: none; 
  
} 
.remember-forgot a:hover{ text-decoration: underline; 
} 
.wrapper .btn{ width: 100%; height: 45px; 
background: #fff; border: none; outline: none; border-radius: 40px; box-shadow: 0 0 10px rgba(0, 0, 0, .1); cursor: pointer; font-size: 16px; color: #333; font-weight: 600; 
} 
.wrapper .register-link{ font-size: 14.5px; text-align: center; margin: 20px 0 15px; 
 
} 
.register-link p a{ color: #fff; text-decoration: none; font-weight: 600; 
} 
.register-link p a:hover{ text-decoration: underline; 
} 
 
 
 
const countries = { 
"am-ET": "Amharic", 
"ar-SA": "Arabic", "be-BY": "Bielarus", 
"bem-ZM": "Bemba", 
"bi-VU": 	"Bislama", 
"bjs-BB": 	"Bajan", 
"bn-IN": 	"Bengali", 
"bo-CN": 	"Tibetan", 
"br-FR": 	"Breton", 
"bs-BA": 	"Bosnian", 
"ca-ES": 	"Catalan", 
"cop-EG": 	"Coptic", 
"cs-CZ": "Czech", 
"cy-GB": "Welsh", 
"da-DK": "Danish", 
"dz-BT": "Dzongkha", "de-DE": "German", 
"dv-MV": "Maldivian", 
"el-GR": "Greek", 
"en-GB": "English", 
"es-ES": "Spanish", 
"et-EE": "Estonian", 
"eu-ES": "Basque", "fa-IR": "Persian", 
"fi-FI": "Finnish", 
"fn-FNG": "Fanagalo", 
"fo-FO": "Faroese", 
	"fr-FR": 	"French", 
"gl-ES": "Galician", 
"gu-IN": "Gujarati", 
"ha-NE": "Hausa", "he-IL": "Hebrew", 
"hi-IN": "Hindi", 
	"hr-HR": 	"Croatian", 
"hu-HU": "Hungarian", 
	"id-ID": 	"Indonesian", 
"is-IS": "Icelandic", 
"it-IT": "Italian", 
"ja-JP": "Japanese", "kk-KZ": "Kazakh", "km-KM": "Khmer", "kn-IN": "Kannada", 
"ko-KR": "Korean", 
"ku-TR": "Kurdish", 
"ky-KG": "Kyrgyz", "la-VA": "Latin", 
"lo-LA": "Lao", 
"lv-LV": "Latvian", "men-SL": "Mende", 
"mg-MG": "Malagasy", "mi-NZ": "Maori", 
"ms-MY": "Malay", "mt-MT": "Maltese", 
"my-MM": "Burmese", 
"ne-NP": "Nepali", 
"niu-NU": "Niuean", 
"nl-NL": "Dutch", 
"no-NO": "Norwegian", 
"ny-MW": "Nyanja", "ur-PK": "Pakistani", "pau-PW": "Palauan", 
"pa-IN": "Panjabi", "ps-PK": "Pashto", 
	"pis-SB": 	"Pijin", 
"pl-PL": "Polish", 
"pt-PT": "Portuguese", "rn-BI": "Kirundi", 
"ro-RO": "Romanian", 
	"ru-RU": 	"Russian", 
"sg-CF": "Sango", 
	"si-LK": 	"Sinhala", 
	"sk-SK": 	"Slovak", 
"sm-WS": "Samoan", 
	"sn-ZW": 	"Shona", 
"so-SO": "Somali", "sq-AL": "Albanian", 
"sr-RS": "Serbian", "sv-SE": "Swedish", 
	"sw-SZ": 	"Swahili", 
"ta-LK": "Tamil", 
"te-IN": "Telugu", 
"tet-TL": "Tetum", 
	"tg-TJ": 	"Tajik", 
"th-TH": "Thai", 
	"ti-TI": 	"Tigrinya", 
"tk-TM": "Turkmen", 
"tl-PH": "Tagalog", "tn-BW": "Tswana", "to-TO": "Tongan", 
"tr-TR": "Turkish", 
"uk-UA": "Ukrainian", 
"uz-UZ": "Uzbek", 
"vi-VN": "Vietnamese", 
"wo-SN": "Wolof", 
"xh-ZA": "Xhosa", 
"yi-YD": "Yiddish", 
"zu-ZA": "Zulu" 
} 
const fromText = document.querySelector(".from-text"), toText =   document.querySelector(".to-text"), exchageIcon  = document.querySelector(".exchange"), selectTag = document.querySelectorAll("select"), icons = document.querySelectorAll(".row i"); translateBtn = document.querySelector("button"), selectTag.forEach((tag, id) => { for (let country_code in countries) { let selected = id == 0 ? country_code == "en-GB" ? "selected" : "" : 
country_code == "hi-IN" ? "selected" : ""; let option = `<option ${selected} 
value="${country_code}">${countries[country_code]}</option>`; tag.insertAdjacentHTML("beforeend", option); 
} 
}); 
 
 
exchageIcon.addEventListener("click", () => { 
let 	tempText 	=  	fromText.value, 
tempLang 	= 	 selectTag[0].value; 
fromText.value 	= 	toText.value; 
toText.value 	= 	tempText; 
 
selectTag[0].value = selectTag[1].value; selectTag[1].value = tempLang; 
}); 
 
 
fromText.addEventListener("keyup", () => { 
if(!fromText.value) { 
toText.value = ""; 
} 
}); 
translateBtn.addEventListener("click", () => { let 	text 	= 	fromText.value.trim(), translateFrom 	= 	selectTag[0].value, 
translateTo = selectTag[1].value; if(!text) return; 
toText.setAttribute("placeholder", "Translating..."); let apiUrl = 
`https://api.mymemory.translated.net/get?q=${text}&langpair=${translateFrom}|${tr anslateTo}`; fetch(apiUrl).then(res => res.json()).then(data => { toText.value = data.responseData.translatedText; data.matches.forEach(data => { if(data.id === 0) { toText.value = data.translation; 
} 
}); 
toText.setAttribute("placeholder", "Translation"); 
}); 
}); 
 
 
icons.forEach(icon => { icon.addEventListener("click", ({target}) => { if(!fromText.value || !toText.value) return; if(target.classList.contains("fa-copy")) { if(target.id == "from") { navigator.clipboard.writeText(fromText.value); 
} else { 
navigator.clipboard.writeText(toText.value); 
} 
} else { let utterance; if(target.id == "from") { 
utterance = new SpeechSynthesisUtterance(fromText.value); utterance.lang = selectTag[0].value; 
} else { 
utterance = new SpeechSynthesisUtterance(toText.value); utterance.lang = selectTag[1].value; 
 	 	} 
 	 
 	 	speechSynthesis.speak(utterance); 
}  
 
}); 
 
}); 
 
 
 
 
// app.js 
const pdfInput = document.getElementById('pdfInput'); const pdfText = 	document.getElementById('input'); const pdfjsLib = window['pdfjs-dist/build/pdf']; 
 
pdfInput.addEventListener('change', handlePdfUpload); 
 
 
function handlePdfUpload(event) { 
const file = event.target.files[0]; 
if (file && file.type === 'application/pdf') { const 	reader 	= 	new 	FileReader(); 
reader.onload = async () => { 
const buffer = reader.result; await extractTextFromPdf(buffer); 
}; 
reader.readAsArrayBuffer(file); 
} else { pdfText.textContent = 'Please select a valid PDF file.'; 
} 
} 
 
 
async function extractTextFromPdf(buffer) { const pdf = await pdfjsLib.getDocument(buffer).promise; let fullText = ''; 
 
for (let i = 1; i <= pdf.numPages; i++) { 
const page = await pdf.getPage(i); const textContent = await page.getTextContent(); const pageText = textContent.items.map(item => item.str).join(' '); fullText += pageText + '\n'; 
} 
 
 
pdfText.textContent = fullText; console.log(pdfText.textContent) 
} 

