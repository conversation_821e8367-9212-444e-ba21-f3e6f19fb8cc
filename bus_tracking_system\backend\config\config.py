import os
from datetime import timedelta

class Config:
    """Base configuration class"""
    
    # Basic Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # Database Configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///bus_tracking.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ECHO = False
    
    # JWT Configuration
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-change-in-production'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    
    # CORS Configuration
    CORS_ORIGINS = ['http://localhost:3000', 'http://localhost:5000']
    
    # GPS and Tracking Configuration
    GPS_UPDATE_INTERVAL_SECONDS = 30
    GPS_ACCURACY_THRESHOLD_METERS = 10
    MAX_SPEED_THRESHOLD_KMH = 120
    
    # Route and Stop Configuration
    STOP_PROXIMITY_THRESHOLD_METERS = 100
    ARRIVAL_PREDICTION_BUFFER_MINUTES = 5
    
    # Alert and Notification Configuration
    EMERGENCY_RESPONSE_TIME_MINUTES = 15
    ALERT_RETENTION_DAYS = 30
    
    # Performance Configuration
    MAX_LOCATION_HISTORY_DAYS = 7
    CLEANUP_INTERVAL_HOURS = 24
    
    # External Services
    MAPS_API_KEY = os.environ.get('MAPS_API_KEY')
    SMS_API_KEY = os.environ.get('SMS_API_KEY')
    EMAIL_API_KEY = os.environ.get('EMAIL_API_KEY')
    
    # File Upload Configuration
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    UPLOAD_FOLDER = 'uploads'
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'csv'}

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    SQLALCHEMY_ECHO = True
    
    # Use SQLite for development
    SQLALCHEMY_DATABASE_URI = 'sqlite:///bus_tracking_dev.db'
    
    # Relaxed CORS for development
    CORS_ORIGINS = ['*']

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    
    # Use PostgreSQL for production
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://user:password@localhost/bus_tracking'
    
    # Strict CORS for production
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '').split(',')

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
