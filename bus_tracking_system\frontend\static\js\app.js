// Main application class
class BusTrackingApp {
    constructor() {
        this.currentSection = 'home';
        this.updateIntervals = new Map();
        this.selectedStop = null;
        this.selectedRoute = null;
        this.isLoading = false;
    }

    // Initialize the application
    async init() {
        console.log('Initializing Bus Tracking App...');
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Connect to WebSocket
        websocket.connect();
        
        // Load initial data
        await this.loadInitialData();
        
        // Setup auto-refresh intervals
        this.setupAutoRefresh();
        
        console.log('App initialized successfully');
    }

    // Setup event listeners
    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.getAttribute('href').substring(1);
                this.showSection(section);
            });
        });

        // Search functionality
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        
        if (searchInput && searchBtn) {
            const debouncedSearch = UTILS.debounce(
                () => this.performSearch(searchInput.value),
                CONFIG.SEARCH.DEBOUNCE_DELAY
            );
            
            searchInput.addEventListener('input', debouncedSearch);
            searchBtn.addEventListener('click', () => this.performSearch(searchInput.value));
        }

        // Location button
        const locationBtn = document.getElementById('locationBtn');
        if (locationBtn) {
            locationBtn.addEventListener('click', () => this.getUserLocation());
        }

        // Map controls
        const showAllBusesBtn = document.getElementById('showAllBuses');
        const showNearbyStopsBtn = document.getElementById('showNearbyStops');
        
        if (showAllBusesBtn) {
            showAllBusesBtn.addEventListener('click', () => this.showAllBuses());
        }
        
        if (showNearbyStopsBtn) {
            showNearbyStopsBtn.addEventListener('click', () => this.showNearbyStops());
        }

        // Custom events from map
        window.addEventListener('busMarkerClick', (e) => this.handleBusMarkerClick(e.detail));
        window.addEventListener('stopMarkerClick', (e) => this.handleStopMarkerClick(e.detail));
        window.addEventListener('showBusDetails', (e) => this.showBusDetails(e.detail.busId));
        window.addEventListener('showStopDetails', (e) => this.showStopDetails(e.detail.stopId));

        // WebSocket events
        window.addEventListener('busLocationUpdate', (e) => this.handleBusLocationUpdate(e.detail));
        window.addEventListener('busStatusUpdate', (e) => this.handleBusStatusUpdate(e.detail));
        window.addEventListener('alertUpdate', (e) => this.handleAlertUpdate(e.detail));

        // Modal close
        const modal = document.getElementById('modal');
        const closeBtn = modal?.querySelector('.close');
        
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.closeModal());
        }
        
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) this.closeModal();
            });
        }
    }

    // Load initial data
    async loadInitialData() {
        try {
            this.setLoading(true);
            
            // Load routes for popular routes section
            await this.loadPopularRoutes();
            
            // Load active alerts
            await this.loadActiveAlerts();
            
            // Load all stops for map
            await this.loadAllStops();
            
        } catch (error) {
            console.error('Error loading initial data:', error);
            UTILS.showNotification('Error loading data. Please refresh the page.', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    // Setup auto-refresh intervals
    setupAutoRefresh() {
        // Live tracking updates
        this.updateIntervals.set('liveTracking', setInterval(() => {
            if (this.currentSection === 'home') {
                this.updateLiveTracking();
            }
        }, CONFIG.UPDATE_INTERVALS.LIVE_TRACKING));

        // Arrivals updates
        this.updateIntervals.set('arrivals', setInterval(() => {
            if (this.selectedStop) {
                this.updateStopArrivals(this.selectedStop);
            }
        }, CONFIG.UPDATE_INTERVALS.ARRIVALS));

        // Alerts updates
        this.updateIntervals.set('alerts', setInterval(() => {
            this.loadActiveAlerts();
        }, CONFIG.UPDATE_INTERVALS.ALERTS));
    }

    // Show specific section
    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionName;
        }

        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[href="#${sectionName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        // Load section-specific data
        this.loadSectionData(sectionName);
    }

    // Load data for specific section
    async loadSectionData(sectionName) {
        switch (sectionName) {
            case 'routes':
                await this.loadAllRoutes();
                break;
            case 'stops':
                await this.loadAllStopsForSection();
                break;
            case 'alerts':
                await this.loadAllAlerts();
                break;
        }
    }

    // Perform search
    async performSearch(query) {
        if (!query || query.length < CONFIG.SEARCH.MIN_QUERY_LENGTH) return;

        try {
            this.setLoading(true);
            
            // Search in parallel
            const [routeResults, stopResults] = await Promise.all([
                api.searchRoutes(query),
                api.searchStops(query)
            ]);

            this.displaySearchResults(routeResults.data, stopResults.data);
            
        } catch (error) {
            console.error('Search error:', error);
            UTILS.showNotification('Search failed. Please try again.', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    // Get user's current location
    async getUserLocation() {
        try {
            const location = await UTILS.getCurrentLocation();
            
            if (mapService) {
                mapService.addUserMarker(location.latitude, location.longitude);
            }
            
            // Load nearby stops and buses
            await this.loadNearbyData(location.latitude, location.longitude);
            
            UTILS.showNotification('Location updated successfully', 'success');
            
        } catch (error) {
            console.error('Geolocation error:', error);
            UTILS.showNotification('Could not get your location. Please enable location services.', 'error');
        }
    }

    // Load nearby data
    async loadNearbyData(latitude, longitude) {
        try {
            const [nearbyStops, nearbyBuses] = await Promise.all([
                api.getNearbyStops(latitude, longitude, 2),
                api.getNearbyBuses(latitude, longitude, 5)
            ]);

            // Add stops to map
            if (mapService) {
                nearbyStops.data.forEach(stop => {
                    mapService.addStopMarker(stop);
                });

                // Add buses to map
                nearbyBuses.data.forEach(bus => {
                    if (bus.current_location) {
                        mapService.addBusMarker(bus, bus.current_location);
                    }
                });
            }

        } catch (error) {
            console.error('Error loading nearby data:', error);
        }
    }

    // Show all buses on map
    async showAllBuses() {
        try {
            this.setLoading(true);
            
            const response = await api.getLiveTracking();
            
            if (mapService) {
                mapService.clearBusMarkers();
                
                response.data.forEach(busData => {
                    if (busData.location) {
                        mapService.addBusMarker(busData, busData.location);
                    }
                });
                
                mapService.fitToMarkers();
            }
            
        } catch (error) {
            console.error('Error loading buses:', error);
            UTILS.showNotification('Error loading bus locations', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    // Show nearby stops
    async showNearbyStops() {
        const userLocation = UTILS.getSavedLocation();
        
        if (!userLocation) {
            UTILS.showNotification('Please enable location first', 'warning');
            return;
        }

        try {
            this.setLoading(true);
            
            const response = await api.getNearbyStops(
                userLocation.latitude, 
                userLocation.longitude, 
                2
            );
            
            if (mapService) {
                mapService.clearStopMarkers();
                
                response.data.forEach(stop => {
                    mapService.addStopMarker(stop);
                });
                
                mapService.fitToMarkers();
            }
            
        } catch (error) {
            console.error('Error loading nearby stops:', error);
            UTILS.showNotification('Error loading nearby stops', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    // Load popular routes
    async loadPopularRoutes() {
        try {
            const response = await api.getAllRoutes();
            const routes = response.data.slice(0, 5); // Show first 5 routes
            
            const container = document.getElementById('popularRoutes');
            if (container) {
                container.innerHTML = routes.map(route => `
                    <div class="route-item" onclick="app.selectRoute('${route.id}')">
                        <span class="route-number">${route.route_number}</span>
                        <div>
                            <strong>${route.route_name}</strong>
                            <br>
                            <small>${route.start_location} → ${route.end_location}</small>
                        </div>
                    </div>
                `).join('');
            }
            
        } catch (error) {
            console.error('Error loading popular routes:', error);
        }
    }

    // Load active alerts
    async loadActiveAlerts() {
        try {
            const response = await api.getActiveAlerts();
            
            const container = document.getElementById('serviceAlerts');
            if (container) {
                if (response.data.length === 0) {
                    container.innerHTML = '<p class="no-alerts">No active alerts</p>';
                } else {
                    container.innerHTML = response.data.map(alert => `
                        <div class="alert-item">
                            <span class="alert-badge alert-${alert.alert_type.toLowerCase()}">${alert.alert_type}</span>
                            <div>
                                <strong>${alert.title}</strong>
                                <br>
                                <small>${alert.message}</small>
                            </div>
                        </div>
                    `).join('');
                }
            }
            
        } catch (error) {
            console.error('Error loading alerts:', error);
        }
    }

    // Set loading state
    setLoading(isLoading) {
        this.isLoading = isLoading;
        // You can add loading indicators here
    }

    // Handle bus marker click
    handleBusMarkerClick(bus) {
        this.showBusDetails(bus.bus_id || bus.id);
    }

    // Handle stop marker click
    handleStopMarkerClick(stop) {
        this.selectStop(stop.id);
    }

    // Select a stop and show arrivals
    async selectStop(stopId) {
        this.selectedStop = stopId;
        await this.updateStopArrivals(stopId);
    }

    // Update stop arrivals
    async updateStopArrivals(stopId) {
        try {
            const response = await api.getStopArrivals(stopId);
            
            const container = document.getElementById('nextArrivals');
            if (container) {
                if (response.data.length === 0) {
                    container.innerHTML = '<p class="no-arrivals">No upcoming arrivals</p>';
                } else {
                    container.innerHTML = response.data.map(arrival => {
                        const minutes = UTILS.getMinutesFromNow(arrival.estimated_arrival);
                        return `
                            <div class="arrival-item">
                                <div>
                                    <strong>Bus ${arrival.bus_number}</strong>
                                    <br>
                                    <small>Route ${arrival.route_number}</small>
                                </div>
                                <span class="arrival-time">${minutes} min</span>
                            </div>
                        `;
                    }).join('');
                }
            }
            
        } catch (error) {
            console.error('Error loading arrivals:', error);
        }
    }

    // Close modal
    closeModal() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // Handle real-time updates
    handleBusLocationUpdate(data) {
        if (mapService && data.location) {
            mapService.addBusMarker(data, data.location);
        }
    }

    handleBusStatusUpdate(data) {
        // Update UI with new bus status
        console.log('Bus status update:', data);
    }

    handleAlertUpdate(data) {
        // Refresh alerts
        this.loadActiveAlerts();
        UTILS.showNotification(`New alert: ${data.title}`, 'warning');
    }
}

// Initialize app when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.app = new BusTrackingApp();
    app.init();
});
