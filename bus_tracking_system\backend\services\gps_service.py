import math
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from ..models.base import db
from ..models.bus import Bus
from ..models.tracking import GPSLocation, BusStatus
from ..models.route import Route, RouteStop
from ..models.stop import BusStop

class GPSTrackingService:
    """Service for handling GPS tracking and location updates"""
    
    def __init__(self):
        self.active_simulations = {}  # Store active simulation data
        
    def record_gps_location(self, bus_id: str, latitude: float, longitude: float, 
                          speed: float = 0.0, heading: float = 0.0, 
                          accuracy: float = 5.0, source: str = 'GPS') -> GPSLocation:
        """Record a new GPS location for a bus"""
        
        # Validate coordinates
        if not self._validate_coordinates(latitude, longitude):
            raise ValueError("Invalid GPS coordinates")
        
        # Create new GPS location record
        gps_location = GPSLocation(
            bus_id=bus_id,
            latitude=latitude,
            longitude=longitude,
            speed=speed,
            heading=heading,
            accuracy=accuracy,
            source=source,
            timestamp=datetime.utcnow()
        )
        
        gps_location.save()
        
        # Update bus status based on new location
        self._update_bus_status_from_location(bus_id, gps_location)
        
        return gps_location
    
    def get_bus_current_location(self, bus_id: str) -> Optional[GPSLocation]:
        """Get the most recent GPS location for a bus"""
        return GPSLocation.query.filter_by(
            bus_id=bus_id, 
            is_active=True
        ).order_by(db.desc(GPSLocation.timestamp)).first()
    
    def get_bus_location_history(self, bus_id: str, hours: int = 24) -> List[GPSLocation]:
        """Get GPS location history for a bus"""
        since = datetime.utcnow() - timedelta(hours=hours)
        
        return GPSLocation.query.filter(
            GPSLocation.bus_id == bus_id,
            GPSLocation.timestamp >= since,
            GPSLocation.is_active == True
        ).order_by(db.desc(GPSLocation.timestamp)).all()
    
    def calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two GPS coordinates using Haversine formula"""
        R = 6371  # Earth's radius in kilometers
        
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        
        a = (math.sin(dlat/2)**2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    def calculate_bearing(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate bearing between two GPS coordinates"""
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        dlon_rad = math.radians(lon2 - lon1)
        
        y = math.sin(dlon_rad) * math.cos(lat2_rad)
        x = (math.cos(lat1_rad) * math.sin(lat2_rad) - 
             math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(dlon_rad))
        
        bearing_rad = math.atan2(y, x)
        bearing_deg = math.degrees(bearing_rad)
        
        return (bearing_deg + 360) % 360
    
    def find_nearest_stop(self, latitude: float, longitude: float, max_distance_km: float = 0.5) -> Optional[BusStop]:
        """Find the nearest bus stop to given coordinates"""
        all_stops = BusStop.query.filter_by(is_active=True).all()
        
        nearest_stop = None
        min_distance = float('inf')
        
        for stop in all_stops:
            distance = self.calculate_distance(latitude, longitude, stop.latitude, stop.longitude)
            if distance < min_distance and distance <= max_distance_km:
                min_distance = distance
                nearest_stop = stop
        
        return nearest_stop
    
    def estimate_arrival_time(self, bus_id: str, target_stop_id: str) -> Optional[datetime]:
        """Estimate arrival time at a target stop"""
        bus = Bus.get_by_id(bus_id)
        if not bus or not bus.current_route_id:
            return None
        
        current_location = self.get_bus_current_location(bus_id)
        if not current_location:
            return None
        
        target_stop = BusStop.get_by_id(target_stop_id)
        if not target_stop:
            return None
        
        # Calculate distance to target stop
        distance_km = self.calculate_distance(
            current_location.latitude, current_location.longitude,
            target_stop.latitude, target_stop.longitude
        )
        
        # Estimate travel time based on current speed or average city speed
        current_speed = current_location.speed if current_location.speed > 0 else 25  # Default 25 km/h
        travel_time_hours = distance_km / current_speed
        
        # Add buffer for stops and traffic
        buffer_minutes = distance_km * 2  # 2 minutes per km for stops/traffic
        total_travel_time = timedelta(hours=travel_time_hours, minutes=buffer_minutes)
        
        return datetime.utcnow() + total_travel_time
    
    def start_gps_simulation(self, bus_id: str, route_id: str, speed_kmh: float = 30.0) -> bool:
        """Start GPS simulation for a bus following a route"""
        bus = Bus.get_by_id(bus_id)
        route = Route.get_by_id(route_id)
        
        if not bus or not route:
            return False
        
        # Get route stops in order
        route_stops = route.get_stops_in_order()
        if not route_stops:
            return False
        
        # Initialize simulation data
        self.active_simulations[bus_id] = {
            'route_id': route_id,
            'stops': route_stops,
            'current_stop_index': 0,
            'speed_kmh': speed_kmh,
            'last_update': datetime.utcnow(),
            'is_at_stop': False,
            'stop_departure_time': None
        }
        
        # Set initial position at first stop
        first_stop = route_stops[0].stop
        self.record_gps_location(
            bus_id=bus_id,
            latitude=first_stop.latitude,
            longitude=first_stop.longitude,
            speed=0.0,
            source='SIMULATION'
        )
        
        return True
    
    def update_gps_simulation(self, bus_id: str) -> bool:
        """Update GPS simulation for a bus"""
        if bus_id not in self.active_simulations:
            return False
        
        sim_data = self.active_simulations[bus_id]
        now = datetime.utcnow()
        time_elapsed = (now - sim_data['last_update']).total_seconds()
        
        # If at a stop, wait for departure time
        if sim_data['is_at_stop']:
            if sim_data['stop_departure_time'] and now >= sim_data['stop_departure_time']:
                sim_data['is_at_stop'] = False
                sim_data['current_stop_index'] += 1
                
                # Check if route is complete
                if sim_data['current_stop_index'] >= len(sim_data['stops']):
                    self.stop_gps_simulation(bus_id)
                    return True
            else:
                return True  # Still waiting at stop
        
        # Calculate new position
        current_stop_index = sim_data['current_stop_index']
        if current_stop_index >= len(sim_data['stops']):
            return False
        
        current_stop = sim_data['stops'][current_stop_index].stop
        
        # Get current location
        current_location = self.get_bus_current_location(bus_id)
        if not current_location:
            return False
        
        # Calculate distance to current target stop
        distance_to_stop = self.calculate_distance(
            current_location.latitude, current_location.longitude,
            current_stop.latitude, current_stop.longitude
        )
        
        # If close to stop, arrive at stop
        if distance_to_stop < 0.1:  # Within 100 meters
            self.record_gps_location(
                bus_id=bus_id,
                latitude=current_stop.latitude,
                longitude=current_stop.longitude,
                speed=0.0,
                source='SIMULATION'
            )
            
            # Set stop departure time (30 seconds dwell time)
            sim_data['is_at_stop'] = True
            sim_data['stop_departure_time'] = now + timedelta(seconds=30)
            
        else:
            # Move towards the stop
            bearing = self.calculate_bearing(
                current_location.latitude, current_location.longitude,
                current_stop.latitude, current_stop.longitude
            )
            
            # Calculate new position
            distance_traveled_km = (sim_data['speed_kmh'] / 3600) * time_elapsed
            new_lat, new_lon = self._calculate_new_position(
                current_location.latitude, current_location.longitude,
                bearing, distance_traveled_km
            )
            
            self.record_gps_location(
                bus_id=bus_id,
                latitude=new_lat,
                longitude=new_lon,
                speed=sim_data['speed_kmh'],
                heading=bearing,
                source='SIMULATION'
            )
        
        sim_data['last_update'] = now
        return True
    
    def stop_gps_simulation(self, bus_id: str) -> bool:
        """Stop GPS simulation for a bus"""
        if bus_id in self.active_simulations:
            del self.active_simulations[bus_id]
            return True
        return False
    
    def _validate_coordinates(self, latitude: float, longitude: float) -> bool:
        """Validate GPS coordinates"""
        return (-90 <= latitude <= 90) and (-180 <= longitude <= 180)
    
    def _calculate_new_position(self, lat: float, lon: float, bearing: float, distance_km: float) -> Tuple[float, float]:
        """Calculate new position given current position, bearing, and distance"""
        R = 6371  # Earth's radius in kilometers
        
        lat_rad = math.radians(lat)
        lon_rad = math.radians(lon)
        bearing_rad = math.radians(bearing)
        
        new_lat_rad = math.asin(
            math.sin(lat_rad) * math.cos(distance_km / R) +
            math.cos(lat_rad) * math.sin(distance_km / R) * math.cos(bearing_rad)
        )
        
        new_lon_rad = lon_rad + math.atan2(
            math.sin(bearing_rad) * math.sin(distance_km / R) * math.cos(lat_rad),
            math.cos(distance_km / R) - math.sin(lat_rad) * math.sin(new_lat_rad)
        )
        
        return math.degrees(new_lat_rad), math.degrees(new_lon_rad)
    
    def _update_bus_status_from_location(self, bus_id: str, gps_location: GPSLocation):
        """Update bus status based on new GPS location"""
        bus = Bus.get_by_id(bus_id)
        if not bus:
            return
        
        # Find nearest stop
        nearest_stop = self.find_nearest_stop(gps_location.latitude, gps_location.longitude)
        
        # Determine operational status
        operational_status = 'ON_ROUTE'
        if gps_location.speed < 5:  # Less than 5 km/h
            if nearest_stop:
                operational_status = 'AT_STOP'
            else:
                operational_status = 'BREAK'
        
        # Create or update bus status
        bus_status = BusStatus(
            bus_id=bus_id,
            operational_status=operational_status,
            current_route_id=bus.current_route_id,
            current_stop_id=nearest_stop.id if nearest_stop else None,
            driver_id=bus.current_driver_id,
            timestamp=gps_location.timestamp
        )
        
        bus_status.save()
