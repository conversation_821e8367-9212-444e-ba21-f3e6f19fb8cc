"""
Advanced AI-Powered Translation Platform
Modern FastAPI backend with AI/ML capabilities
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.security import HTTPBearer
import structlog

from core.config import get_settings
from core.database import init_db, close_db
from core.redis_client import init_redis, close_redis
from core.ai_service import AITranslationService
from core.auth import AuthService
from api.v1 import api_router
from services.translation_service import TranslationService
from services.document_service import DocumentService
from services.voice_service import VoiceService
from services.analytics_service import AnalyticsService
from middleware.auth import AuthMiddleware
from middleware.rate_limit import RateLimitMiddleware
from middleware.logging import LoggingMiddleware

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Global services
ai_service = AITranslationService()
auth_service = AuthService()
translation_service = TranslationService()
document_service = DocumentService()
voice_service = VoiceService()
analytics_service = AnalyticsService()

security = HTTPBearer()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("Starting Advanced Translation Platform...")
    
    # Initialize services
    await init_db()
    await init_redis()
    await ai_service.initialize()
    await translation_service.initialize()
    await document_service.initialize()
    await voice_service.initialize()
    await analytics_service.initialize()
    
    # Start background tasks
    asyncio.create_task(analytics_service.start_real_time_processing())
    asyncio.create_task(ai_service.start_model_optimization())
    
    logger.info("Translation platform initialized successfully")
    
    yield
    
    # Cleanup
    logger.info("Shutting down translation platform...")
    await close_redis()
    await close_db()
    await ai_service.cleanup()
    await analytics_service.cleanup()
    logger.info("Platform shutdown complete")


def create_application() -> FastAPI:
    """Create and configure FastAPI application"""
    settings = get_settings()
    
    app = FastAPI(
        title="Advanced AI Translation Platform",
        description="Modern translation platform with AI/ML capabilities and real-time features",
        version="2.0.0",
        docs_url="/api/docs",
        redoc_url="/api/redoc",
        openapi_url="/api/openapi.json",
        lifespan=lifespan
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    app.add_middleware(AuthMiddleware)
    app.add_middleware(RateLimitMiddleware)
    app.add_middleware(LoggingMiddleware)
    
    # Include routers
    app.include_router(api_router, prefix="/api/v1")
    
    # Core translation endpoints
    @app.post("/api/v1/translate")
    async def translate_text(
        request: Dict[str, Any],
        current_user = Depends(auth_service.get_current_user)
    ):
        """Translate text using AI models"""
        try:
            source_text = request.get("text", "")
            source_lang = request.get("source_language", "auto")
            target_lang = request.get("target_language", "en")
            model_type = request.get("model_type", "neural")
            
            if not source_text:
                raise HTTPException(status_code=400, detail="Text is required")
            
            # Perform translation
            result = await translation_service.translate(
                text=source_text,
                source_lang=source_lang,
                target_lang=target_lang,
                model_type=model_type,
                user_id=current_user.id
            )
            
            # Log analytics
            await analytics_service.log_translation(
                user_id=current_user.id,
                source_lang=source_lang,
                target_lang=target_lang,
                text_length=len(source_text),
                model_type=model_type
            )
            
            return result
            
        except Exception as e:
            logger.error("Translation failed", error=str(e))
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/v1/translate/document")
    async def translate_document(
        file: UploadFile = File(...),
        target_language: str = "en",
        current_user = Depends(auth_service.get_current_user)
    ):
        """Translate document files"""
        try:
            # Validate file type
            allowed_types = ["pdf", "docx", "txt", "xlsx", "pptx"]
            file_extension = file.filename.split(".")[-1].lower()
            
            if file_extension not in allowed_types:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Unsupported file type. Allowed: {', '.join(allowed_types)}"
                )
            
            # Process document
            result = await document_service.translate_document(
                file=file,
                target_language=target_language,
                user_id=current_user.id
            )
            
            return result
            
        except Exception as e:
            logger.error("Document translation failed", error=str(e))
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/v1/translate/voice")
    async def translate_voice(
        audio_file: UploadFile = File(...),
        target_language: str = "en",
        current_user = Depends(auth_service.get_current_user)
    ):
        """Translate voice/audio files"""
        try:
            # Validate audio file
            allowed_audio_types = ["mp3", "wav", "m4a", "ogg"]
            file_extension = audio_file.filename.split(".")[-1].lower()
            
            if file_extension not in allowed_audio_types:
                raise HTTPException(
                    status_code=400,
                    detail=f"Unsupported audio type. Allowed: {', '.join(allowed_audio_types)}"
                )
            
            # Process voice translation
            result = await voice_service.translate_voice(
                audio_file=audio_file,
                target_language=target_language,
                user_id=current_user.id
            )
            
            return result
            
        except Exception as e:
            logger.error("Voice translation failed", error=str(e))
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/v1/languages")
    async def get_supported_languages():
        """Get list of supported languages"""
        return await translation_service.get_supported_languages()
    
    @app.get("/api/v1/translation/history")
    async def get_translation_history(
        page: int = 1,
        limit: int = 20,
        current_user = Depends(auth_service.get_current_user)
    ):
        """Get user's translation history"""
        try:
            history = await analytics_service.get_user_translation_history(
                user_id=current_user.id,
                page=page,
                limit=limit
            )
            return history
        except Exception as e:
            logger.error("Failed to get translation history", error=str(e))
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/v1/analytics/dashboard")
    async def get_analytics_dashboard(
        current_user = Depends(auth_service.get_current_user)
    ):
        """Get analytics dashboard data"""
        try:
            dashboard_data = await analytics_service.get_dashboard_data(
                user_id=current_user.id
            )
            return dashboard_data
        except Exception as e:
            logger.error("Failed to get dashboard data", error=str(e))
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/v1/translate/batch")
    async def batch_translate(
        request: Dict[str, Any],
        current_user = Depends(auth_service.get_current_user)
    ):
        """Batch translate multiple texts"""
        try:
            texts = request.get("texts", [])
            source_lang = request.get("source_language", "auto")
            target_lang = request.get("target_language", "en")
            
            if not texts:
                raise HTTPException(status_code=400, detail="Texts array is required")
            
            # Process batch translation
            results = await translation_service.batch_translate(
                texts=texts,
                source_lang=source_lang,
                target_lang=target_lang,
                user_id=current_user.id
            )
            
            return {"results": results}
            
        except Exception as e:
            logger.error("Batch translation failed", error=str(e))
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/v1/translate/stream")
    async def stream_translate(
        text: str,
        source_language: str = "auto",
        target_language: str = "en",
        current_user = Depends(auth_service.get_current_user)
    ):
        """Stream translation for real-time translation"""
        try:
            async def generate_translation():
                async for chunk in translation_service.stream_translate(
                    text=text,
                    source_lang=source_language,
                    target_lang=target_language,
                    user_id=current_user.id
                ):
                    yield f"data: {chunk}\n\n"
            
            return StreamingResponse(
                generate_translation(),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache"}
            )
            
        except Exception as e:
            logger.error("Stream translation failed", error=str(e))
            raise HTTPException(status_code=500, detail=str(e))
    
    # Health check endpoints
    @app.get("/health")
    async def health_check():
        """Basic health check"""
        return {"status": "healthy", "service": "translation-platform"}
    
    @app.get("/health/detailed")
    async def detailed_health_check():
        """Detailed health check with service status"""
        try:
            # Check all services
            db_status = await check_database_health()
            redis_status = await check_redis_health()
            ai_status = await ai_service.health_check()
            
            return {
                "status": "healthy",
                "services": {
                    "database": db_status,
                    "redis": redis_status,
                    "ai_service": ai_status,
                    "translation_models": await translation_service.get_model_status()
                }
            }
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            raise HTTPException(status_code=503, detail="Service unhealthy")
    
    # Global exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        logger.error("Unhandled exception", error=str(exc), path=request.url.path)
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"}
        )
    
    return app


async def check_database_health() -> Dict[str, Any]:
    """Check database connectivity"""
    try:
        from core.database import get_db_session
        async with get_db_session() as session:
            await session.execute("SELECT 1")
        return {"status": "healthy"}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}


async def check_redis_health() -> Dict[str, Any]:
    """Check Redis connectivity"""
    try:
        from core.redis_client import get_redis
        redis = await get_redis()
        await redis.ping()
        return {"status": "healthy"}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}


# Create the application instance
app = create_application()

if __name__ == "__main__":
    settings = get_settings()
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_config=None,  # Use structlog instead
        access_log=False,  # Handled by middleware
    )
