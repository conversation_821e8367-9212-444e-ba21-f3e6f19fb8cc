import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Provider } from 'react-redux';
import { HelmetProvider } from 'react-helmet-async';
import { Toaster } from 'react-hot-toast';

import { store } from './store/store';
import { AuthProvider } from './contexts/AuthContext';
import { SocketProvider } from './contexts/SocketContext';
import { LanguageProvider } from './contexts/LanguageContext';

import Navbar from './components/Layout/Navbar';
import Sidebar from './components/Layout/Sidebar';
import LoadingSpinner from './components/Common/LoadingSpinner';

// Pages
import Dashboard from './pages/Dashboard';
import Translator from './pages/Translator';
import DocumentTranslator from './pages/DocumentTranslator';
import VoiceTranslator from './pages/VoiceTranslator';
import BatchTranslator from './pages/BatchTranslator';
import TranslationHistory from './pages/TranslationHistory';
import Analytics from './pages/Analytics';
import Settings from './pages/Settings';
import Profile from './pages/Profile';
import Login from './pages/Auth/Login';
import Register from './pages/Auth/Register';
import ForgotPassword from './pages/Auth/ForgotPassword';
import ProtectedRoute from './components/Auth/ProtectedRoute';

// Create theme
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
    },
    secondary: {
      main: '#dc004e',
      light: '#ff5983',
      dark: '#9a0036',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 600,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
          padding: '8px 24px',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          borderRadius: 12,
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
          },
        },
      },
    },
  },
});

// Create query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

const App: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <HelmetProvider>
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider theme={theme}>
            <CssBaseline />
            <AuthProvider>
              <SocketProvider>
                <LanguageProvider>
                  <Router>
                    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
                      <Routes>
                        {/* Auth Routes */}
                        <Route path="/login" element={<Login />} />
                        <Route path="/register" element={<Register />} />
                        <Route path="/forgot-password" element={<ForgotPassword />} />
                        
                        {/* Protected Routes */}
                        <Route
                          path="/*"
                          element={
                            <ProtectedRoute>
                              <Box sx={{ display: 'flex', width: '100%' }}>
                                <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} />
                                <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                                  <Navbar onMenuClick={handleSidebarToggle} />
                                  <Box
                                    component="main"
                                    sx={{
                                      flexGrow: 1,
                                      p: 3,
                                      backgroundColor: 'background.default',
                                      minHeight: 'calc(100vh - 64px)',
                                    }}
                                  >
                                    <Routes>
                                      <Route path="/" element={<Navigate to="/dashboard" replace />} />
                                      <Route path="/dashboard" element={<Dashboard />} />
                                      <Route path="/translator" element={<Translator />} />
                                      <Route path="/document-translator" element={<DocumentTranslator />} />
                                      <Route path="/voice-translator" element={<VoiceTranslator />} />
                                      <Route path="/batch-translator" element={<BatchTranslator />} />
                                      <Route path="/history" element={<TranslationHistory />} />
                                      <Route path="/analytics" element={<Analytics />} />
                                      <Route path="/settings" element={<Settings />} />
                                      <Route path="/profile" element={<Profile />} />
                                    </Routes>
                                  </Box>
                                </Box>
                              </Box>
                            </ProtectedRoute>
                          }
                        />
                      </Routes>
                    </Box>
                    
                    {/* Global Components */}
                    <Toaster
                      position="top-right"
                      toastOptions={{
                        duration: 4000,
                        style: {
                          background: '#363636',
                          color: '#fff',
                        },
                        success: {
                          duration: 3000,
                          iconTheme: {
                            primary: '#4caf50',
                            secondary: '#fff',
                          },
                        },
                        error: {
                          duration: 5000,
                          iconTheme: {
                            primary: '#f44336',
                            secondary: '#fff',
                          },
                        },
                      }}
                    />
                    
                    <React.Suspense fallback={<LoadingSpinner />}>
                      {/* Lazy loaded components will show loading spinner */}
                    </React.Suspense>
                  </Router>
                </LanguageProvider>
              </SocketProvider>
            </AuthProvider>
          </ThemeProvider>
        </QueryClientProvider>
      </Provider>
    </HelmetProvider>
  );
};

export default App;
