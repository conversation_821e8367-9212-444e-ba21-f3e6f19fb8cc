// Map service for handling Leaflet map functionality
class MapService {
    constructor(containerId) {
        this.containerId = containerId;
        this.map = null;
        this.busMarkers = new Map();
        this.stopMarkers = new Map();
        this.userMarker = null;
        this.routeLines = new Map();
        this.isInitialized = false;
    }

    // Initialize the map
    init() {
        if (this.isInitialized) return;

        // Create map instance
        this.map = L.map(this.containerId).setView(
            CONFIG.MAP.DEFAULT_CENTER,
            CONFIG.MAP.DEFAULT_ZOOM
        );

        // Add tile layer
        L.tileLayer(CONFIG.MAP.TILE_LAYER, {
            attribution: CONFIG.MAP.ATTRIBUTION,
            maxZoom: CONFIG.MAP.MAX_ZOOM,
            minZoom: CONFIG.MAP.MIN_ZOOM
        }).addTo(this.map);

        // Create custom icons
        this.createCustomIcons();

        this.isInitialized = true;
        console.log('Map initialized');
    }

    // Create custom icons for different markers
    createCustomIcons() {
        this.busIcon = L.icon(CONFIG.ICONS.BUS);
        this.stopIcon = L.icon(CONFIG.ICONS.STOP);
        this.userIcon = L.icon(CONFIG.ICONS.USER);
    }

    // Add or update bus marker
    addBusMarker(bus, location) {
        const busId = bus.bus_id || bus.id;
        const lat = location.latitude;
        const lon = location.longitude;

        // Create popup content
        const popupContent = this.createBusPopup(bus, location);

        if (this.busMarkers.has(busId)) {
            // Update existing marker
            const marker = this.busMarkers.get(busId);
            marker.setLatLng([lat, lon]);
            marker.setPopupContent(popupContent);
        } else {
            // Create new marker
            const marker = L.marker([lat, lon], { icon: this.busIcon })
                .bindPopup(popupContent)
                .addTo(this.map);

            // Add click event
            marker.on('click', () => {
                this.onBusMarkerClick(bus);
            });

            this.busMarkers.set(busId, marker);
        }
    }

    // Add stop marker
    addStopMarker(stop) {
        const stopId = stop.id;
        const lat = stop.latitude;
        const lon = stop.longitude;

        if (this.stopMarkers.has(stopId)) return;

        const popupContent = this.createStopPopup(stop);

        const marker = L.marker([lat, lon], { icon: this.stopIcon })
            .bindPopup(popupContent)
            .addTo(this.map);

        // Add click event
        marker.on('click', () => {
            this.onStopMarkerClick(stop);
        });

        this.stopMarkers.set(stopId, marker);
    }

    // Add user location marker
    addUserMarker(latitude, longitude) {
        if (this.userMarker) {
            this.map.removeLayer(this.userMarker);
        }

        this.userMarker = L.marker([latitude, longitude], { icon: this.userIcon })
            .bindPopup('Your Location')
            .addTo(this.map);

        // Center map on user location
        this.map.setView([latitude, longitude], 15);
    }

    // Create bus popup content
    createBusPopup(bus, location) {
        const busNumber = bus.bus_number || 'Unknown';
        const speed = location.speed ? `${Math.round(location.speed)} km/h` : 'Stationary';
        const lastUpdate = UTILS.formatTime(location.timestamp);

        return `
            <div class="bus-popup">
                <h4><i class="fas fa-bus"></i> Bus ${busNumber}</h4>
                <p><strong>Speed:</strong> ${speed}</p>
                <p><strong>Last Update:</strong> ${lastUpdate}</p>
                <p><strong>Status:</strong> ${bus.status || 'Unknown'}</p>
                <button onclick="showBusDetails('${bus.bus_id || bus.id}')" class="btn btn-primary btn-sm">
                    View Details
                </button>
            </div>
        `;
    }

    // Create stop popup content
    createStopPopup(stop) {
        const facilities = [];
        if (stop.has_shelter) facilities.push('Shelter');
        if (stop.has_seating) facilities.push('Seating');
        if (stop.has_digital_display) facilities.push('Digital Display');
        if (stop.has_wheelchair_access) facilities.push('Wheelchair Access');

        return `
            <div class="stop-popup">
                <h4><i class="fas fa-map-marker-alt"></i> ${stop.stop_name}</h4>
                <p><strong>Code:</strong> ${stop.stop_code}</p>
                ${stop.address ? `<p><strong>Address:</strong> ${stop.address}</p>` : ''}
                ${facilities.length > 0 ? `<p><strong>Facilities:</strong> ${facilities.join(', ')}</p>` : ''}
                <button onclick="showStopDetails('${stop.id}')" class="btn btn-primary btn-sm">
                    View Arrivals
                </button>
            </div>
        `;
    }

    // Remove bus marker
    removeBusMarker(busId) {
        if (this.busMarkers.has(busId)) {
            this.map.removeLayer(this.busMarkers.get(busId));
            this.busMarkers.delete(busId);
        }
    }

    // Remove stop marker
    removeStopMarker(stopId) {
        if (this.stopMarkers.has(stopId)) {
            this.map.removeLayer(this.stopMarkers.get(stopId));
            this.stopMarkers.delete(stopId);
        }
    }

    // Clear all bus markers
    clearBusMarkers() {
        this.busMarkers.forEach(marker => {
            this.map.removeLayer(marker);
        });
        this.busMarkers.clear();
    }

    // Clear all stop markers
    clearStopMarkers() {
        this.stopMarkers.forEach(marker => {
            this.map.removeLayer(marker);
        });
        this.stopMarkers.clear();
    }

    // Draw route line
    drawRoute(routeId, coordinates, color = CONFIG.COLORS.PRIMARY) {
        if (this.routeLines.has(routeId)) {
            this.map.removeLayer(this.routeLines.get(routeId));
        }

        const polyline = L.polyline(coordinates, {
            color: color,
            weight: 4,
            opacity: 0.7
        }).addTo(this.map);

        this.routeLines.set(routeId, polyline);

        // Fit map to route bounds
        this.map.fitBounds(polyline.getBounds());
    }

    // Remove route line
    removeRoute(routeId) {
        if (this.routeLines.has(routeId)) {
            this.map.removeLayer(this.routeLines.get(routeId));
            this.routeLines.delete(routeId);
        }
    }

    // Center map on coordinates
    centerOn(latitude, longitude, zoom = 15) {
        this.map.setView([latitude, longitude], zoom);
    }

    // Fit map to show all markers
    fitToMarkers() {
        const allMarkers = [
            ...this.busMarkers.values(),
            ...this.stopMarkers.values()
        ];

        if (this.userMarker) {
            allMarkers.push(this.userMarker);
        }

        if (allMarkers.length > 0) {
            const group = new L.featureGroup(allMarkers);
            this.map.fitBounds(group.getBounds().pad(0.1));
        }
    }

    // Event handlers
    onBusMarkerClick(bus) {
        console.log('Bus marker clicked:', bus);
        // This will be handled by the main app
        window.dispatchEvent(new CustomEvent('busMarkerClick', { detail: bus }));
    }

    onStopMarkerClick(stop) {
        console.log('Stop marker clicked:', stop);
        // This will be handled by the main app
        window.dispatchEvent(new CustomEvent('stopMarkerClick', { detail: stop }));
    }

    // Get map bounds
    getBounds() {
        return this.map.getBounds();
    }

    // Check if coordinates are in current view
    isInView(latitude, longitude) {
        const bounds = this.getBounds();
        return bounds.contains([latitude, longitude]);
    }

    // Add click event to map
    onMapClick(callback) {
        this.map.on('click', callback);
    }

    // Resize map (useful when container size changes)
    resize() {
        if (this.map) {
            this.map.invalidateSize();
        }
    }
}

// Global map instance
let mapService = null;

// Initialize map when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    const mapContainer = document.getElementById('map');
    if (mapContainer) {
        mapService = new MapService('map');
        mapService.init();
    }
});

// Global functions for popup buttons
window.showBusDetails = (busId) => {
    // This will be implemented in app.js
    window.dispatchEvent(new CustomEvent('showBusDetails', { detail: { busId } }));
};

window.showStopDetails = (stopId) => {
    // This will be implemented in app.js
    window.dispatchEvent(new CustomEvent('showStopDetails', { detail: { stopId } }));
};
