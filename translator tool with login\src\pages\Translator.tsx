import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Typography,
  IconButton,
  Chip,
  Divider,
  LinearProgress,
  Alert,
  Tooltip,
  Grid,
  Paper,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  SwapHoriz,
  VolumeUp,
  ContentCopy,
  Share,
  Favorite,
  FavoriteBorder,
  History,
  AutoAwesome,
  Translate,
  Speed,
  Psychology,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useSpeechSynthesis } from 'react-speech-kit';
import toast from 'react-hot-toast';

import { useTranslation } from '../hooks/useTranslation';
import { useLanguages } from '../hooks/useLanguages';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/Common/LoadingSpinner';
import LanguageSelector from '../components/Translator/LanguageSelector';
import TranslationAlternatives from '../components/Translator/TranslationAlternatives';
import ConfidenceIndicator from '../components/Translator/ConfidenceIndicator';

interface TranslationResult {
  translated_text: string;
  original_text: string;
  source_language: string;
  target_language: string;
  confidence: number;
  model_used: string;
  alternatives?: Array<{
    text: string;
    model: string;
    confidence: number;
  }>;
}

const Translator: React.FC = () => {
  const { user } = useAuth();
  const { speak, cancel, speaking } = useSpeechSynthesis();
  
  // State
  const [sourceText, setSourceText] = useState('');
  const [translatedText, setTranslatedText] = useState('');
  const [sourceLanguage, setSourceLanguage] = useState('auto');
  const [targetLanguage, setTargetLanguage] = useState('en');
  const [modelType, setModelType] = useState('neural');
  const [isRealTime, setIsRealTime] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [translationResult, setTranslationResult] = useState<TranslationResult | null>(null);
  
  // Hooks
  const { data: languages, isLoading: languagesLoading } = useLanguages();
  const { 
    mutate: translate, 
    isLoading: translating, 
    error: translationError 
  } = useTranslation();

  // Real-time translation effect
  useEffect(() => {
    if (isRealTime && sourceText.trim() && sourceText.length > 3) {
      const timeoutId = setTimeout(() => {
        handleTranslate();
      }, 1000); // Debounce for 1 second

      return () => clearTimeout(timeoutId);
    }
  }, [sourceText, isRealTime, sourceLanguage, targetLanguage, modelType]);

  const handleTranslate = useCallback(() => {
    if (!sourceText.trim()) {
      toast.error('Please enter text to translate');
      return;
    }

    translate({
      text: sourceText,
      source_language: sourceLanguage,
      target_language: targetLanguage,
      model_type: modelType,
    }, {
      onSuccess: (data: TranslationResult) => {
        setTranslatedText(data.translated_text);
        setTranslationResult(data);
        toast.success('Translation completed!');
      },
      onError: (error: any) => {
        toast.error(error.message || 'Translation failed');
      },
    });
  }, [sourceText, sourceLanguage, targetLanguage, modelType, translate]);

  const handleSwapLanguages = () => {
    if (sourceLanguage === 'auto') {
      toast.warning('Cannot swap when source language is auto-detect');
      return;
    }
    
    setSourceLanguage(targetLanguage);
    setTargetLanguage(sourceLanguage);
    setSourceText(translatedText);
    setTranslatedText(sourceText);
  };

  const handleCopyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard!');
  };

  const handleSpeak = (text: string, language: string) => {
    if (speaking) {
      cancel();
    } else {
      speak({ text, voice: getVoiceForLanguage(language) });
    }
  };

  const getVoiceForLanguage = (language: string) => {
    const voices = speechSynthesis.getVoices();
    return voices.find(voice => voice.lang.startsWith(language)) || voices[0];
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Translation',
          text: `${sourceText} → ${translatedText}`,
        });
      } catch (error) {
        handleCopyToClipboard(`${sourceText} → ${translatedText}`);
      }
    } else {
      handleCopyToClipboard(`${sourceText} → ${translatedText}`);
    }
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
    toast.success(isFavorite ? 'Removed from favorites' : 'Added to favorites');
  };

  const modelTypeOptions = [
    { value: 'neural', label: 'Neural MT', icon: <Psychology />, description: 'High quality, slower' },
    { value: 'transformer', label: 'Transformer', icon: <AutoAwesome />, description: 'Balanced quality and speed' },
    { value: 'fast', label: 'Fast MT', icon: <Speed />, description: 'Fast, good quality' },
  ];

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 2 }}>
      {/* Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h3" component="h1" gutterBottom>
          <Translate sx={{ mr: 2, verticalAlign: 'middle' }} />
          AI Translator
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Advanced AI-powered translation with multiple models
        </Typography>
      </Box>

      {/* Main Translation Interface */}
      <Card elevation={3} sx={{ mb: 3 }}>
        <CardContent sx={{ p: 3 }}>
          {/* Controls */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={4}>
              <LanguageSelector
                value={sourceLanguage}
                onChange={setSourceLanguage}
                languages={languages}
                label="From"
                includeAuto
                disabled={languagesLoading}
              />
            </Grid>
            
            <Grid item xs={12} md={1} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Tooltip title="Swap languages">
                <IconButton 
                  onClick={handleSwapLanguages}
                  disabled={sourceLanguage === 'auto'}
                  color="primary"
                >
                  <SwapHoriz />
                </IconButton>
              </Tooltip>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <LanguageSelector
                value={targetLanguage}
                onChange={setTargetLanguage}
                languages={languages}
                label="To"
                disabled={languagesLoading}
              />
            </Grid>
            
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Model Type</InputLabel>
                <Select
                  value={modelType}
                  onChange={(e) => setModelType(e.target.value)}
                  label="Model Type"
                >
                  {modelTypeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {option.icon}
                        <Box>
                          <Typography variant="body2">{option.label}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {option.description}
                          </Typography>
                        </Box>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* Real-time toggle */}
          <Box sx={{ mb: 3 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={isRealTime}
                  onChange={(e) => setIsRealTime(e.target.checked)}
                  color="primary"
                />
              }
              label="Real-time translation"
            />
          </Box>

          {/* Translation Areas */}
          <Grid container spacing={3}>
            {/* Source Text */}
            <Grid item xs={12} md={6}>
              <Paper elevation={1} sx={{ p: 2, height: 300 }}>
                <TextField
                  fullWidth
                  multiline
                  rows={10}
                  variant="outlined"
                  placeholder="Enter text to translate..."
                  value={sourceText}
                  onChange={(e) => setSourceText(e.target.value)}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      height: '100%',
                      '& fieldset': { border: 'none' },
                    },
                  }}
                />
                
                {/* Source Controls */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                  <Typography variant="caption" color="text.secondary">
                    {sourceText.length} characters
                  </Typography>
                  <Box>
                    <Tooltip title="Speak">
                      <IconButton
                        size="small"
                        onClick={() => handleSpeak(sourceText, sourceLanguage)}
                        disabled={!sourceText.trim()}
                      >
                        <VolumeUp />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Copy">
                      <IconButton
                        size="small"
                        onClick={() => handleCopyToClipboard(sourceText)}
                        disabled={!sourceText.trim()}
                      >
                        <ContentCopy />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </Paper>
            </Grid>

            {/* Translated Text */}
            <Grid item xs={12} md={6}>
              <Paper elevation={1} sx={{ p: 2, height: 300, position: 'relative' }}>
                {translating && (
                  <Box sx={{ position: 'absolute', top: 0, left: 0, right: 0, zIndex: 1 }}>
                    <LinearProgress />
                  </Box>
                )}
                
                <TextField
                  fullWidth
                  multiline
                  rows={10}
                  variant="outlined"
                  placeholder="Translation will appear here..."
                  value={translatedText}
                  InputProps={{ readOnly: true }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      height: '100%',
                      '& fieldset': { border: 'none' },
                      backgroundColor: translating ? 'action.hover' : 'transparent',
                    },
                  }}
                />
                
                {/* Translation Controls */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {translationResult && (
                      <ConfidenceIndicator confidence={translationResult.confidence} />
                    )}
                    {translationResult && (
                      <Chip
                        label={translationResult.model_used}
                        size="small"
                        variant="outlined"
                      />
                    )}
                  </Box>
                  <Box>
                    <Tooltip title="Speak">
                      <IconButton
                        size="small"
                        onClick={() => handleSpeak(translatedText, targetLanguage)}
                        disabled={!translatedText.trim()}
                      >
                        <VolumeUp />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Copy">
                      <IconButton
                        size="small"
                        onClick={() => handleCopyToClipboard(translatedText)}
                        disabled={!translatedText.trim()}
                      >
                        <ContentCopy />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Share">
                      <IconButton
                        size="small"
                        onClick={handleShare}
                        disabled={!translatedText.trim()}
                      >
                        <Share />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title={isFavorite ? "Remove from favorites" : "Add to favorites"}>
                      <IconButton
                        size="small"
                        onClick={toggleFavorite}
                        disabled={!translatedText.trim()}
                      >
                        {isFavorite ? <Favorite color="error" /> : <FavoriteBorder />}
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </Paper>
            </Grid>
          </Grid>

          {/* Translate Button */}
          {!isRealTime && (
            <Box sx={{ textAlign: 'center', mt: 3 }}>
              <Button
                variant="contained"
                size="large"
                onClick={handleTranslate}
                disabled={!sourceText.trim() || translating}
                startIcon={translating ? <LoadingSpinner size={20} /> : <Translate />}
                sx={{ minWidth: 200 }}
              >
                {translating ? 'Translating...' : 'Translate'}
              </Button>
            </Box>
          )}

          {/* Error Display */}
          <AnimatePresence>
            {translationError && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >
                <Alert severity="error" sx={{ mt: 2 }}>
                  {translationError.message || 'Translation failed. Please try again.'}
                </Alert>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>

      {/* Alternative Translations */}
      {translationResult?.alternatives && translationResult.alternatives.length > 0 && (
        <TranslationAlternatives
          alternatives={translationResult.alternatives}
          onSelect={(alternative) => {
            setTranslatedText(alternative.text);
            toast.success(`Switched to ${alternative.model} translation`);
          }}
        />
      )}
    </Box>
  );
};

export default Translator;
