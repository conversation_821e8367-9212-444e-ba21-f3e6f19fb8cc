from .base import BaseModel, db
import math

class BusStop(BaseModel):
    """Bus stop model for storing bus stop information"""
    __tablename__ = 'bus_stops'
    
    # Basic Information
    stop_code = db.Column(db.String(20), unique=True, nullable=False)
    stop_name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    
    # Location Information
    latitude = db.Column(db.Float, nullable=False)
    longitude = db.Column(db.Float, nullable=False)
    address = db.Column(db.String(200))
    landmark = db.Column(db.String(100))
    
    # Infrastructure Information
    has_shelter = db.Column(db.Boolean, default=False)
    has_seating = db.Column(db.Boolean, default=False)
    has_lighting = db.Column(db.Boolean, default=False)
    has_digital_display = db.Column(db.<PERSON>, default=False)
    has_wheelchair_access = db.Column(db.<PERSON>, default=False)
    
    # Operational Information
    is_major_stop = db.Column(db.Boolean, default=False)
    zone = db.Column(db.String(50))  # City zone or area
    status = db.Column(db.String(20), default='ACTIVE')  # ACTIVE, MAINTENANCE, CLOSED
    
    # Safety and Security
    has_cctv = db.Column(db.Boolean, default=False)
    has_emergency_button = db.Column(db.Boolean, default=False)
    safety_rating = db.Column(db.Integer, default=5)  # 1-10 scale
    
    def __repr__(self):
        return f'<BusStop {self.stop_code}: {self.stop_name}>'
    
    def calculate_distance_to(self, latitude, longitude):
        """Calculate distance to another point using Haversine formula"""
        R = 6371  # Earth's radius in kilometers
        
        lat1_rad = math.radians(self.latitude)
        lon1_rad = math.radians(self.longitude)
        lat2_rad = math.radians(latitude)
        lon2_rad = math.radians(longitude)
        
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        
        a = (math.sin(dlat/2)**2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    def get_nearby_stops(self, radius_km=1.0):
        """Get nearby bus stops within specified radius"""
        all_stops = BusStop.query.filter(
            BusStop.id != self.id,
            BusStop.is_active == True
        ).all()
        
        nearby_stops = []
        for stop in all_stops:
            distance = self.calculate_distance_to(stop.latitude, stop.longitude)
            if distance <= radius_km:
                nearby_stops.append({
                    'stop': stop,
                    'distance_km': round(distance, 2)
                })
        
        # Sort by distance
        nearby_stops.sort(key=lambda x: x['distance_km'])
        return nearby_stops
    
    def get_routes_serving_stop(self):
        """Get all routes that serve this stop"""
        from .route import Route
        return Route.query.join(
            Route.route_stops
        ).filter(
            Route.route_stops.any(stop_id=self.id),
            Route.is_active == True
        ).all()
    
    def get_next_arrivals(self, limit=5):
        """Get next bus arrivals at this stop"""
        from .tracking import BusStatus
        from .bus import Bus
        from datetime import datetime, timedelta
        
        # Get buses currently on routes that serve this stop
        serving_routes = self.get_routes_serving_stop()
        route_ids = [route.id for route in serving_routes]
        
        active_buses = Bus.query.filter(
            Bus.current_route_id.in_(route_ids),
            Bus.status == 'IN_SERVICE',
            Bus.is_active == True
        ).all()
        
        arrivals = []
        for bus in active_buses:
            # Calculate estimated arrival time (simplified)
            current_location = bus.get_current_location()
            if current_location:
                distance = self.calculate_distance_to(
                    current_location.latitude, 
                    current_location.longitude
                )
                # Assume average speed of 30 km/h in city traffic
                estimated_minutes = (distance / 30) * 60
                estimated_arrival = datetime.utcnow() + timedelta(minutes=estimated_minutes)
                
                arrivals.append({
                    'bus': bus,
                    'route': bus.current_route,
                    'estimated_arrival': estimated_arrival,
                    'distance_km': round(distance, 2),
                    'estimated_minutes': round(estimated_minutes)
                })
        
        # Sort by estimated arrival time
        arrivals.sort(key=lambda x: x['estimated_arrival'])
        return arrivals[:limit]
    
    def to_dict(self):
        """Convert to dictionary with additional computed fields"""
        data = super().to_dict()
        data['serving_routes'] = [route.route_number for route in self.get_routes_serving_stop()]
        data['next_arrivals'] = []
        
        # Add next arrivals (simplified for performance)
        try:
            arrivals = self.get_next_arrivals(3)
            for arrival in arrivals:
                data['next_arrivals'].append({
                    'bus_number': arrival['bus'].bus_number,
                    'route_number': arrival['route'].route_number if arrival['route'] else 'Unknown',
                    'estimated_minutes': arrival['estimated_minutes'],
                    'distance_km': arrival['distance_km']
                })
        except Exception:
            # In case of any error, just return empty arrivals
            pass
            
        return data
