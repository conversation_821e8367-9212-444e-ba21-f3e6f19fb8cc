"""
Advanced Machine Learning service for predictive analytics
"""

import asyncio
import pickle
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import structlog

from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error

from backend.core.config import get_settings
from backend.core.database import get_db_session
from backend.core.redis_client import get_cache

logger = structlog.get_logger()


class MLPredictionService:
    """Advanced ML service for bus tracking predictions"""
    
    def __init__(self):
        self.settings = get_settings()
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.is_initialized = False
        self.prediction_task: Optional[asyncio.Task] = None
    
    async def initialize(self) -> None:
        """Initialize ML models and services"""
        try:
            await self.load_models()
            await self.prepare_feature_processors()
            self.is_initialized = True
            logger.info("ML service initialized successfully")
        except Exception as e:
            logger.error("ML service initialization failed", error=str(e))
            raise
    
    async def load_models(self) -> None:
        """Load pre-trained models or train new ones"""
        try:
            # Try to load existing models
            model_files = {
                'arrival_time': 'arrival_time_model.pkl',
                'delay_prediction': 'delay_prediction_model.pkl',
                'passenger_demand': 'passenger_demand_model.pkl',
                'route_optimization': 'route_optimization_model.pkl'
            }
            
            for model_name, filename in model_files.items():
                try:
                    with open(f"{self.settings.ML_MODEL_PATH}/{filename}", 'rb') as f:
                        self.models[model_name] = pickle.load(f)
                    logger.info("Loaded model", model=model_name)
                except FileNotFoundError:
                    # Train new model if file doesn't exist
                    await self.train_model(model_name)
                    
        except Exception as e:
            logger.error("Model loading failed", error=str(e))
            # Initialize with default models
            await self.initialize_default_models()
    
    async def initialize_default_models(self) -> None:
        """Initialize default ML models"""
        self.models = {
            'arrival_time': RandomForestRegressor(n_estimators=100, random_state=42),
            'delay_prediction': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'passenger_demand': RandomForestRegressor(n_estimators=100, random_state=42),
            'route_optimization': RandomForestRegressor(n_estimators=100, random_state=42)
        }
        logger.info("Initialized default ML models")
    
    async def prepare_feature_processors(self) -> None:
        """Prepare feature scaling and encoding"""
        self.scalers = {
            'numerical': StandardScaler(),
            'time_features': StandardScaler(),
            'location_features': StandardScaler()
        }
        
        self.encoders = {
            'route_id': LabelEncoder(),
            'bus_id': LabelEncoder(),
            'weather_condition': LabelEncoder(),
            'day_of_week': LabelEncoder()
        }
    
    async def predict_arrival_time(self, bus_id: str, stop_id: str, current_location: Dict[str, float]) -> Dict[str, Any]:
        """Predict bus arrival time at specific stop"""
        try:
            # Get historical data and current conditions
            features = await self.prepare_arrival_features(bus_id, stop_id, current_location)
            
            if 'arrival_time' not in self.models:
                return {"error": "Arrival time model not available"}
            
            # Make prediction
            prediction = self.models['arrival_time'].predict([features])[0]
            confidence = await self.calculate_prediction_confidence('arrival_time', features)
            
            # Convert to actual arrival time
            estimated_arrival = datetime.utcnow() + timedelta(minutes=prediction)
            
            result = {
                "bus_id": bus_id,
                "stop_id": stop_id,
                "estimated_arrival": estimated_arrival.isoformat(),
                "estimated_minutes": round(prediction, 1),
                "confidence": confidence,
                "factors": await self.get_prediction_factors(features)
            }
            
            # Cache prediction
            cache = await get_cache()
            await cache.set(
                f"arrival_prediction:{bus_id}:{stop_id}",
                result,
                ttl=300  # 5 minutes
            )
            
            return result
            
        except Exception as e:
            logger.error("Arrival time prediction failed", error=str(e))
            return {"error": str(e)}
    
    async def predict_delay(self, bus_id: str, route_id: str) -> Dict[str, Any]:
        """Predict potential delays for bus route"""
        try:
            features = await self.prepare_delay_features(bus_id, route_id)
            
            if 'delay_prediction' not in self.models:
                return {"error": "Delay prediction model not available"}
            
            delay_minutes = self.models['delay_prediction'].predict([features])[0]
            confidence = await self.calculate_prediction_confidence('delay_prediction', features)
            
            result = {
                "bus_id": bus_id,
                "route_id": route_id,
                "predicted_delay_minutes": round(delay_minutes, 1),
                "delay_category": self.categorize_delay(delay_minutes),
                "confidence": confidence,
                "contributing_factors": await self.analyze_delay_factors(features)
            }
            
            return result
            
        except Exception as e:
            logger.error("Delay prediction failed", error=str(e))
            return {"error": str(e)}
    
    async def predict_passenger_demand(self, route_id: str, time_slot: datetime) -> Dict[str, Any]:
        """Predict passenger demand for route at specific time"""
        try:
            features = await self.prepare_demand_features(route_id, time_slot)
            
            if 'passenger_demand' not in self.models:
                return {"error": "Passenger demand model not available"}
            
            demand = self.models['passenger_demand'].predict([features])[0]
            confidence = await self.calculate_prediction_confidence('passenger_demand', features)
            
            result = {
                "route_id": route_id,
                "time_slot": time_slot.isoformat(),
                "predicted_passengers": round(demand),
                "demand_level": self.categorize_demand(demand),
                "confidence": confidence,
                "recommendations": await self.generate_demand_recommendations(demand, route_id)
            }
            
            return result
            
        except Exception as e:
            logger.error("Passenger demand prediction failed", error=str(e))
            return {"error": str(e)}
    
    async def optimize_route(self, route_id: str, current_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize route based on current conditions"""
        try:
            features = await self.prepare_optimization_features(route_id, current_conditions)
            
            if 'route_optimization' not in self.models:
                return {"error": "Route optimization model not available"}
            
            optimization_score = self.models['route_optimization'].predict([features])[0]
            
            result = {
                "route_id": route_id,
                "optimization_score": round(optimization_score, 2),
                "recommendations": await self.generate_route_recommendations(optimization_score, current_conditions),
                "alternative_routes": await self.suggest_alternative_routes(route_id, current_conditions)
            }
            
            return result
            
        except Exception as e:
            logger.error("Route optimization failed", error=str(e))
            return {"error": str(e)}
    
    async def prepare_arrival_features(self, bus_id: str, stop_id: str, current_location: Dict[str, float]) -> List[float]:
        """Prepare features for arrival time prediction"""
        features = []
        
        # Time-based features
        now = datetime.utcnow()
        features.extend([
            now.hour,
            now.minute,
            now.weekday(),
            now.month
        ])
        
        # Location features
        features.extend([
            current_location.get('latitude', 0),
            current_location.get('longitude', 0)
        ])
        
        # Historical average for this route/stop
        avg_time = await self.get_historical_average_time(bus_id, stop_id)
        features.append(avg_time)
        
        # Traffic conditions (mock data for now)
        traffic_factor = await self.get_traffic_factor()
        features.append(traffic_factor)
        
        # Weather conditions (mock data for now)
        weather_factor = await self.get_weather_factor()
        features.append(weather_factor)
        
        return features
    
    async def prepare_delay_features(self, bus_id: str, route_id: str) -> List[float]:
        """Prepare features for delay prediction"""
        features = []
        
        # Current time features
        now = datetime.utcnow()
        features.extend([now.hour, now.weekday()])
        
        # Route characteristics
        route_length = await self.get_route_length(route_id)
        features.append(route_length)
        
        # Historical delay patterns
        avg_delay = await self.get_historical_delay(route_id)
        features.append(avg_delay)
        
        # Current conditions
        features.extend([
            await self.get_traffic_factor(),
            await self.get_weather_factor(),
            await self.get_passenger_load_factor(bus_id)
        ])
        
        return features
    
    async def prepare_demand_features(self, route_id: str, time_slot: datetime) -> List[float]:
        """Prepare features for passenger demand prediction"""
        features = []
        
        # Time features
        features.extend([
            time_slot.hour,
            time_slot.weekday(),
            time_slot.month
        ])
        
        # Route features
        features.append(await self.get_route_popularity(route_id))
        
        # Historical demand
        features.append(await self.get_historical_demand(route_id, time_slot))
        
        # External factors
        features.extend([
            await self.get_weather_factor(),
            await self.get_event_factor(time_slot)
        ])
        
        return features
    
    async def prepare_optimization_features(self, route_id: str, conditions: Dict[str, Any]) -> List[float]:
        """Prepare features for route optimization"""
        features = []
        
        # Current conditions
        features.extend([
            conditions.get('traffic_density', 0.5),
            conditions.get('weather_score', 0.8),
            conditions.get('passenger_load', 0.6)
        ])
        
        # Route characteristics
        features.extend([
            await self.get_route_length(route_id),
            await self.get_route_complexity(route_id)
        ])
        
        return features
    
    # Helper methods for feature extraction
    async def get_historical_average_time(self, bus_id: str, stop_id: str) -> float:
        """Get historical average travel time"""
        # Mock implementation - would query database
        return 15.0  # minutes
    
    async def get_traffic_factor(self) -> float:
        """Get current traffic factor (0-1)"""
        # Mock implementation - would integrate with traffic API
        return 0.7
    
    async def get_weather_factor(self) -> float:
        """Get weather impact factor (0-1)"""
        # Mock implementation - would integrate with weather API
        return 0.8
    
    async def get_route_length(self, route_id: str) -> float:
        """Get route length in kilometers"""
        # Mock implementation
        return 12.5
    
    async def get_historical_delay(self, route_id: str) -> float:
        """Get historical average delay"""
        # Mock implementation
        return 3.2
    
    async def get_passenger_load_factor(self, bus_id: str) -> float:
        """Get current passenger load factor"""
        # Mock implementation
        return 0.6
    
    async def get_route_popularity(self, route_id: str) -> float:
        """Get route popularity score"""
        # Mock implementation
        return 0.8
    
    async def get_historical_demand(self, route_id: str, time_slot: datetime) -> float:
        """Get historical demand for time slot"""
        # Mock implementation
        return 25.0
    
    async def get_event_factor(self, time_slot: datetime) -> float:
        """Get special event impact factor"""
        # Mock implementation
        return 1.0
    
    async def get_route_complexity(self, route_id: str) -> float:
        """Get route complexity score"""
        # Mock implementation
        return 0.6
    
    async def calculate_prediction_confidence(self, model_name: str, features: List[float]) -> float:
        """Calculate prediction confidence score"""
        # Mock implementation - would use model uncertainty
        return 0.85
    
    async def get_prediction_factors(self, features: List[float]) -> Dict[str, float]:
        """Get factors contributing to prediction"""
        return {
            "traffic": 0.3,
            "weather": 0.2,
            "historical_pattern": 0.4,
            "time_of_day": 0.1
        }
    
    def categorize_delay(self, delay_minutes: float) -> str:
        """Categorize delay severity"""
        if delay_minutes < 2:
            return "on_time"
        elif delay_minutes < 5:
            return "minor_delay"
        elif delay_minutes < 10:
            return "moderate_delay"
        else:
            return "major_delay"
    
    def categorize_demand(self, demand: float) -> str:
        """Categorize passenger demand level"""
        if demand < 10:
            return "low"
        elif demand < 30:
            return "medium"
        elif demand < 50:
            return "high"
        else:
            return "very_high"
    
    async def analyze_delay_factors(self, features: List[float]) -> Dict[str, str]:
        """Analyze factors contributing to delay"""
        return {
            "primary_factor": "traffic_congestion",
            "secondary_factor": "weather_conditions",
            "impact_level": "moderate"
        }
    
    async def generate_demand_recommendations(self, demand: float, route_id: str) -> List[str]:
        """Generate recommendations based on demand prediction"""
        recommendations = []
        
        if demand > 40:
            recommendations.append("Consider deploying additional buses")
            recommendations.append("Increase frequency during peak hours")
        elif demand < 10:
            recommendations.append("Consider reducing frequency")
            recommendations.append("Optimize route efficiency")
        
        return recommendations
    
    async def generate_route_recommendations(self, score: float, conditions: Dict[str, Any]) -> List[str]:
        """Generate route optimization recommendations"""
        recommendations = []
        
        if score < 0.6:
            recommendations.append("Consider alternative route")
            recommendations.append("Adjust departure times")
        
        if conditions.get('traffic_density', 0) > 0.8:
            recommendations.append("Avoid high-traffic areas")
        
        return recommendations
    
    async def suggest_alternative_routes(self, route_id: str, conditions: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Suggest alternative routes"""
        # Mock implementation
        return [
            {
                "route_id": f"{route_id}_alt1",
                "estimated_time_saving": 5,
                "confidence": 0.8
            }
        ]
    
    async def train_model(self, model_name: str) -> None:
        """Train ML model with historical data"""
        try:
            # This would fetch real training data from database
            logger.info("Training model", model=model_name)
            
            # Mock training process
            if model_name not in self.models:
                self.models[model_name] = RandomForestRegressor(n_estimators=100, random_state=42)
            
            # Save trained model
            # with open(f"{self.settings.ML_MODEL_PATH}/{model_name}_model.pkl", 'wb') as f:
            #     pickle.dump(self.models[model_name], f)
            
            logger.info("Model training completed", model=model_name)
            
        except Exception as e:
            logger.error("Model training failed", model=model_name, error=str(e))
    
    async def start_prediction_engine(self) -> None:
        """Start background prediction engine"""
        if self.prediction_task:
            return
        
        self.prediction_task = asyncio.create_task(self._prediction_loop())
        logger.info("Prediction engine started")
    
    async def _prediction_loop(self) -> None:
        """Background prediction processing loop"""
        while True:
            try:
                await asyncio.sleep(self.settings.ML_PREDICTION_INTERVAL)
                
                # Run batch predictions for all active buses
                await self.run_batch_predictions()
                
            except Exception as e:
                logger.error("Prediction loop error", error=str(e))
    
    async def run_batch_predictions(self) -> None:
        """Run predictions for all active buses"""
        try:
            # This would fetch all active buses and run predictions
            logger.debug("Running batch predictions")
            
        except Exception as e:
            logger.error("Batch predictions failed", error=str(e))
    
    async def health_check(self) -> Dict[str, Any]:
        """Check ML service health"""
        return {
            "status": "healthy" if self.is_initialized else "initializing",
            "models_loaded": len(self.models),
            "prediction_engine_running": self.prediction_task is not None and not self.prediction_task.done()
        }
    
    async def cleanup(self) -> None:
        """Cleanup ML service resources"""
        if self.prediction_task:
            self.prediction_task.cancel()
        
        logger.info("ML service cleanup completed")
