"""
Modern async database layer with SQLAlchemy 2.0
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional
import structlog

from sqlalchemy.ext.asyncio import (
    AsyncSession,
    AsyncEngine,
    create_async_engine,
    async_sessionmaker
)
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy.pool import <PERSON>ull<PERSON>ool
from sqlalchemy import MetaData, event
from sqlalchemy.engine import Engine
import sqlite3

from backend.core.config import get_settings

logger = structlog.get_logger()

# Global variables
async_engine: Optional[AsyncEngine] = None
async_session_factory: Optional[async_sessionmaker[AsyncSession]] = None


class Base(DeclarativeBase):
    """Base class for all database models"""
    
    metadata = MetaData(
        naming_convention={
            "ix": "ix_%(column_0_label)s",
            "uq": "uq_%(table_name)s_%(column_0_name)s",
            "ck": "ck_%(table_name)s_%(constraint_name)s",
            "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
            "pk": "pk_%(table_name)s"
        }
    )


# SQLite foreign key support
@event.listens_for(Engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    if isinstance(dbapi_connection, sqlite3.Connection):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.close()


async def init_db() -> None:
    """Initialize database connection and create tables"""
    global async_engine, async_session_factory
    
    settings = get_settings()
    
    try:
        # Create async engine
        async_engine = create_async_engine(
            settings.DATABASE_URL,
            echo=settings.DEBUG,
            pool_size=settings.DATABASE_POOL_SIZE,
            max_overflow=settings.DATABASE_MAX_OVERFLOW,
            pool_timeout=settings.DATABASE_POOL_TIMEOUT,
            pool_pre_ping=True,
            poolclass=NullPool if "sqlite" in settings.DATABASE_URL else None
        )
        
        # Create session factory
        async_session_factory = async_sessionmaker(
            async_engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # Import all models to ensure they're registered
        from backend.models import *  # noqa
        
        # Create all tables
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database initialized successfully")
        
    except Exception as e:
        logger.error("Failed to initialize database", error=str(e))
        raise


async def close_db() -> None:
    """Close database connections"""
    global async_engine
    
    if async_engine:
        await async_engine.dispose()
        logger.info("Database connections closed")


@asynccontextmanager
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session with automatic cleanup"""
    if not async_session_factory:
        raise RuntimeError("Database not initialized")
    
    async with async_session_factory() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency for FastAPI to get database session"""
    async with get_db_session() as session:
        yield session


class DatabaseManager:
    """Advanced database management utilities"""
    
    @staticmethod
    async def health_check() -> bool:
        """Check database connectivity"""
        try:
            async with get_db_session() as session:
                await session.execute("SELECT 1")
            return True
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return False
    
    @staticmethod
    async def get_connection_info() -> dict:
        """Get database connection information"""
        if not async_engine:
            return {"status": "not_initialized"}
        
        pool = async_engine.pool
        return {
            "status": "connected",
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }
    
    @staticmethod
    async def execute_raw_sql(sql: str, params: dict = None) -> list:
        """Execute raw SQL query"""
        async with get_db_session() as session:
            result = await session.execute(sql, params or {})
            return result.fetchall()
    
    @staticmethod
    async def backup_database(backup_path: str) -> bool:
        """Create database backup (PostgreSQL specific)"""
        try:
            settings = get_settings()
            if "postgresql" not in settings.DATABASE_URL:
                logger.warning("Backup only supported for PostgreSQL")
                return False
            
            # This would need to be implemented with pg_dump
            # For now, just log the intention
            logger.info("Database backup requested", path=backup_path)
            return True
            
        except Exception as e:
            logger.error("Database backup failed", error=str(e))
            return False
    
    @staticmethod
    async def get_table_stats() -> dict:
        """Get statistics about database tables"""
        try:
            async with get_db_session() as session:
                # This is PostgreSQL specific
                sql = """
                SELECT 
                    schemaname,
                    tablename,
                    attname,
                    n_distinct,
                    correlation
                FROM pg_stats 
                WHERE schemaname = 'public'
                ORDER BY tablename, attname;
                """
                result = await session.execute(sql)
                return {"stats": [dict(row) for row in result.fetchall()]}
        except Exception as e:
            logger.error("Failed to get table stats", error=str(e))
            return {"error": str(e)}


# Create global database manager instance
db_manager = DatabaseManager()


class AsyncDatabaseSession:
    """Context manager for database sessions with retry logic"""
    
    def __init__(self, max_retries: int = 3, retry_delay: float = 1.0):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.session: Optional[AsyncSession] = None
    
    async def __aenter__(self) -> AsyncSession:
        for attempt in range(self.max_retries):
            try:
                self.session = async_session_factory()
                return self.session
            except Exception as e:
                logger.warning(
                    "Database connection attempt failed",
                    attempt=attempt + 1,
                    error=str(e)
                )
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay)
                else:
                    raise
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            if exc_type:
                await self.session.rollback()
            else:
                await self.session.commit()
            await self.session.close()


# Utility functions
async def create_database_if_not_exists():
    """Create database if it doesn't exist (PostgreSQL)"""
    settings = get_settings()
    if "postgresql" in settings.DATABASE_URL:
        # Extract database name from URL
        # This would need proper implementation for production
        logger.info("Database creation check completed")


async def run_migrations():
    """Run database migrations using Alembic"""
    try:
        # This would integrate with Alembic for proper migrations
        logger.info("Database migrations completed")
    except Exception as e:
        logger.error("Migration failed", error=str(e))
        raise
