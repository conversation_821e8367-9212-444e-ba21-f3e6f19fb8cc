from .base import BaseModel, db
from datetime import datetime

class GPSLocation(BaseModel):
    """GPS location tracking for buses"""
    __tablename__ = 'gps_locations'
    
    # Bus reference
    bus_id = db.Column(db.String(36), db.<PERSON>ey('buses.id'), nullable=False)
    
    # Location data
    latitude = db.Column(db.Float, nullable=False)
    longitude = db.Column(db.Float, nullable=False)
    altitude = db.Column(db.Float)  # meters above sea level
    accuracy = db.Column(db.Float)  # GPS accuracy in meters
    
    # Movement data
    speed = db.Column(db.Float, default=0.0)  # km/h
    heading = db.Column(db.Float)  # degrees (0-360)
    
    # Timestamp
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Data source
    source = db.Column(db.String(20), default='GPS')  # GPS, MANUAL, ESTIMATED
    
    def __repr__(self):
        return f'<GPSLocation Bus:{self.bus_id} at ({self.latitude}, {self.longitude})>'
    
    def to_dict(self):
        """Convert to dictionary"""
        data = super().to_dict()
        data['timestamp'] = self.timestamp.isoformat() if self.timestamp else None
        return data


class BusStatus(BaseModel):
    """Bus operational status tracking"""
    __tablename__ = 'bus_statuses'
    
    # Bus reference
    bus_id = db.Column(db.String(36), db.ForeignKey('buses.id'), nullable=False)
    
    # Status information
    operational_status = db.Column(db.String(20), nullable=False)  # ON_ROUTE, AT_STOP, BREAK, MAINTENANCE
    passenger_count = db.Column(db.Integer, default=0)
    door_status = db.Column(db.String(10), default='CLOSED')  # OPEN, CLOSED
    
    # Route information
    current_route_id = db.Column(db.String(36), db.ForeignKey('routes.id'))
    current_stop_id = db.Column(db.String(36), db.ForeignKey('bus_stops.id'))
    next_stop_id = db.Column(db.String(36), db.ForeignKey('bus_stops.id'))
    
    # Timing
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    estimated_arrival_next_stop = db.Column(db.DateTime)
    delay_minutes = db.Column(db.Integer, default=0)  # positive = late, negative = early
    
    # Driver information
    driver_id = db.Column(db.String(36), db.ForeignKey('drivers.id'))
    
    # Vehicle condition
    fuel_level_percent = db.Column(db.Float)
    engine_temperature = db.Column(db.Float)
    tire_pressure_status = db.Column(db.String(20), default='NORMAL')  # NORMAL, LOW, CRITICAL
    
    # Emergency status
    emergency_status = db.Column(db.String(20), default='NORMAL')  # NORMAL, ALERT, EMERGENCY
    panic_button_pressed = db.Column(db.Boolean, default=False)
    
    # Relationships
    current_route = db.relationship('Route', foreign_keys=[current_route_id], lazy=True)
    current_stop = db.relationship('BusStop', foreign_keys=[current_stop_id], lazy=True)
    next_stop = db.relationship('BusStop', foreign_keys=[next_stop_id], lazy=True)
    driver = db.relationship('Driver', lazy=True)
    
    def __repr__(self):
        return f'<BusStatus Bus:{self.bus_id} Status:{self.operational_status}>'
    
    def is_delayed(self):
        """Check if bus is running late"""
        return self.delay_minutes > 5  # More than 5 minutes late
    
    def is_early(self):
        """Check if bus is running early"""
        return self.delay_minutes < -2  # More than 2 minutes early
    
    def get_occupancy_percentage(self):
        """Calculate bus occupancy percentage"""
        if not self.bus or not self.bus.total_seats:
            return 0
        return min(100, (self.passenger_count / self.bus.total_seats) * 100)
    
    def is_overcrowded(self):
        """Check if bus is overcrowded"""
        return self.get_occupancy_percentage() > 90
    
    def needs_maintenance_check(self):
        """Check if bus needs maintenance attention"""
        issues = []
        
        if self.fuel_level_percent and self.fuel_level_percent < 20:
            issues.append('Low fuel')
        
        if self.engine_temperature and self.engine_temperature > 100:
            issues.append('High engine temperature')
        
        if self.tire_pressure_status in ['LOW', 'CRITICAL']:
            issues.append('Tire pressure issue')
        
        return issues
    
    def to_dict(self):
        """Convert to dictionary with additional computed fields"""
        data = super().to_dict()
        data['timestamp'] = self.timestamp.isoformat() if self.timestamp else None
        data['estimated_arrival_next_stop'] = (
            self.estimated_arrival_next_stop.isoformat() 
            if self.estimated_arrival_next_stop else None
        )
        data['is_delayed'] = self.is_delayed()
        data['is_early'] = self.is_early()
        data['occupancy_percentage'] = round(self.get_occupancy_percentage(), 1)
        data['is_overcrowded'] = self.is_overcrowded()
        data['maintenance_issues'] = self.needs_maintenance_check()
        
        # Add related object info
        if self.current_route:
            data['current_route_info'] = {
                'route_number': self.current_route.route_number,
                'route_name': self.current_route.route_name
            }
        
        if self.current_stop:
            data['current_stop_info'] = {
                'stop_code': self.current_stop.stop_code,
                'stop_name': self.current_stop.stop_name
            }
        
        if self.next_stop:
            data['next_stop_info'] = {
                'stop_code': self.next_stop.stop_code,
                'stop_name': self.next_stop.stop_name
            }
        
        return data
