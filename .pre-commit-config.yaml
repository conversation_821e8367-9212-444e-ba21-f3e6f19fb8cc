# Pre-commit hooks for code quality and consistency
repos:
  # Python code formatting and linting
  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
        language_version: python3.11
        files: ^(bus_tracking_system|translator tool with login/backend)/.*\.py$

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]
        files: ^(bus_tracking_system|translator tool with login/backend)/.*\.py$

  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args: ["--max-line-length=88", "--extend-ignore=E203,W503"]
        files: ^(bus_tracking_system|translator tool with login/backend)/.*\.py$

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        args: ["--ignore-missing-imports"]
        files: ^(bus_tracking_system|translator tool with login/backend)/.*\.py$
        additional_dependencies: [types-all]

  # Python security
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: ["-r", "-f", "json", "-o", "bandit-report.json"]
        files: ^(bus_tracking_system|translator tool with login/backend)/.*\.py$

  # JavaScript/TypeScript formatting and linting
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.55.0
    hooks:
      - id: eslint
        files: ^translator tool with login/src/.*\.(js|jsx|ts|tsx)$
        additional_dependencies:
          - eslint@8.55.0
          - "@typescript-eslint/eslint-plugin@6.13.1"
          - "@typescript-eslint/parser@6.13.1"
          - "eslint-plugin-react@7.33.2"
          - "eslint-plugin-react-hooks@4.6.0"

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        files: ^translator tool with login/src/.*\.(js|jsx|ts|tsx|css|md|json)$

  # General file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
        args: ["--unsafe"]
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-docstring-first
      - id: debug-statements
      - id: requirements-txt-fixer
        files: requirements.*\.txt$

  # Docker
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: ["--ignore", "DL3008", "--ignore", "DL3009"]

  # Kubernetes YAML validation
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.33.0
    hooks:
      - id: yamllint
        files: ^k8s/.*\.yaml$
        args: ["-d", "relaxed"]

  # Secrets detection
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: ["--baseline", ".secrets.baseline"]
        exclude: package-lock.json

  # Commit message validation
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.13.0
    hooks:
      - id: commitizen
        stages: [commit-msg]

  # SQL formatting
  - repo: https://github.com/sqlfluff/sqlfluff
    rev: 2.3.5
    hooks:
      - id: sqlfluff-lint
        files: \.sql$
      - id: sqlfluff-fix
        files: \.sql$

  # Markdown linting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.37.0
    hooks:
      - id: markdownlint
        files: \.md$
        args: ["--fix"]

  # Shell script linting
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.6
    hooks:
      - id: shellcheck
        files: \.sh$

  # License headers
  - repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.5.4
    hooks:
      - id: insert-license
        files: \.py$
        args:
          - --license-filepath
          - LICENSE_HEADER.txt
          - --comment-style
          - "#"

  # Documentation
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        files: ^(bus_tracking_system|translator tool with login/backend)/.*\.py$
        args: ["--convention=google"]

# Configuration for specific hooks
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
