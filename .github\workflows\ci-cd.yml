name: Advanced Platform CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Code Quality and Security
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: 'translator tool with login/package-lock.json'

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install black isort mypy flake8 bandit safety
        pip install -r bus_tracking_system/requirements.txt

    - name: Install Node.js dependencies
      working-directory: 'translator tool with login'
      run: npm ci

    - name: Python code formatting check
      run: |
        black --check bus_tracking_system/
        isort --check-only bus_tracking_system/

    - name: Python linting
      run: |
        flake8 bus_tracking_system/ --max-line-length=88 --extend-ignore=E203,W503
        mypy bus_tracking_system/ --ignore-missing-imports

    - name: Python security check
      run: |
        bandit -r bus_tracking_system/ -f json -o bandit-report.json
        safety check --json --output safety-report.json

    - name: TypeScript/React linting
      working-directory: 'translator tool with login'
      run: |
        npm run lint
        npm run type-check

    - name: SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # Testing
  test:
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: 'translator tool with login/package-lock.json'

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r bus_tracking_system/requirements.txt

    - name: Install Node.js dependencies
      working-directory: 'translator tool with login'
      run: npm ci

    - name: Run Python tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0
        SECRET_KEY: test-secret-key
        ENVIRONMENT: testing
      run: |
        cd bus_tracking_system
        pytest --cov=. --cov-report=xml --cov-report=html

    - name: Run React tests
      working-directory: 'translator tool with login'
      run: npm test -- --coverage --watchAll=false

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        files: ./bus_tracking_system/coverage.xml,./translator tool with login/coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

  # Build and Push Docker Images
  build-and-push:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name != 'pull_request'
    
    strategy:
      matrix:
        service: [bus-tracking, translator-api, translator-frontend]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ${{ matrix.service == 'bus-tracking' && './bus_tracking_system' || './translator tool with login' }}
        file: ${{ matrix.service == 'bus-tracking' && './bus_tracking_system/Dockerfile' || matrix.service == 'translator-frontend' && './translator tool with login/Dockerfile' || './translator tool with login/backend/Dockerfile' }}
        target: ${{ matrix.service == 'translator-frontend' && 'production' || 'production' }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.event_name != 'pull_request'

    steps:
    - uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-bus-tracking:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # Deploy to Staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build-and-push, security-scan]
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
    - uses: actions/checkout@v4

    - name: Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBE_CONFIG_STAGING }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig

    - name: Deploy to staging
      run: |
        export KUBECONFIG=kubeconfig
        
        # Update image tags
        sed -i "s|image: bus-tracking:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-bus-tracking:${{ github.sha }}|g" k8s/bus-tracking-deployment.yaml
        sed -i "s|image: translator:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-translator-api:${{ github.sha }}|g" k8s/translator-deployment.yaml
        sed -i "s|image: translator-frontend:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-translator-frontend:${{ github.sha }}|g" k8s/translator-deployment.yaml
        
        # Apply configurations
        kubectl apply -f k8s/namespace.yaml
        kubectl apply -f k8s/ -n advanced-platform-dev
        
        # Wait for rollout
        kubectl rollout status deployment/bus-tracking-api -n advanced-platform-dev
        kubectl rollout status deployment/translator-api -n advanced-platform-dev
        kubectl rollout status deployment/translator-frontend -n advanced-platform-dev

    - name: Run integration tests
      run: |
        # Run integration tests against staging environment
        echo "Running integration tests..."
        # Add your integration test commands here

  # Deploy to Production
  deploy-production:
    runs-on: ubuntu-latest
    needs: [build-and-push, security-scan]
    if: github.event_name == 'release'
    environment: production

    steps:
    - uses: actions/checkout@v4

    - name: Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBE_CONFIG_PRODUCTION }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig

    - name: Deploy to production
      run: |
        export KUBECONFIG=kubeconfig
        
        # Update image tags with release version
        sed -i "s|image: bus-tracking:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-bus-tracking:${{ github.event.release.tag_name }}|g" k8s/bus-tracking-deployment.yaml
        sed -i "s|image: translator:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-translator-api:${{ github.event.release.tag_name }}|g" k8s/translator-deployment.yaml
        sed -i "s|image: translator-frontend:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-translator-frontend:${{ github.event.release.tag_name }}|g" k8s/translator-deployment.yaml
        
        # Apply configurations
        kubectl apply -f k8s/namespace.yaml
        kubectl apply -f k8s/ -n advanced-platform
        
        # Wait for rollout
        kubectl rollout status deployment/bus-tracking-api -n advanced-platform
        kubectl rollout status deployment/translator-api -n advanced-platform
        kubectl rollout status deployment/translator-frontend -n advanced-platform

    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        message: |
          🚀 Production deployment completed!
          Version: ${{ github.event.release.tag_name }}
          Commit: ${{ github.sha }}

  # Performance Testing
  performance-test:
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/develop'

    steps:
    - uses: actions/checkout@v4

    - name: Run performance tests
      run: |
        # Install k6
        sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
        echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
        sudo apt-get update
        sudo apt-get install k6
        
        # Run performance tests
        k6 run --out json=performance-results.json tests/performance/load-test.js

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: performance-results.json
