from .base import BaseModel, db
from datetime import datetime

class Bus(BaseModel):
    """Bus model for storing bus information"""
    __tablename__ = 'buses'
    
    # Basic Information
    bus_number = db.Column(db.String(20), unique=True, nullable=False)
    license_plate = db.Column(db.String(20), unique=True, nullable=False)
    model = db.Column(db.String(50), nullable=False)
    manufacturer = db.Column(db.String(50), nullable=False)
    year = db.Column(db.Integer, nullable=False)
    
    # Capacity Information
    total_seats = db.Column(db.Integer, nullable=False, default=40)
    wheelchair_accessible_seats = db.Column(db.Integer, default=2)
    standing_capacity = db.Column(db.Integer, default=20)
    
    # Technical Specifications
    fuel_type = db.Column(db.String(20), default='Diesel')
    engine_capacity = db.Column(db.String(20))
    max_speed = db.Column(db.Integer, default=80)
    
    # Operational Status
    status = db.Column(db.String(20), default='AVAILABLE')  # AVAILABLE, IN_SERVICE, MAINTENANCE, OUT_OF_ORDER
    current_route_id = db.Column(db.String(36), db.ForeignKey('routes.id'), nullable=True)
    current_driver_id = db.Column(db.String(36), db.ForeignKey('drivers.id'), nullable=True)
    
    # GPS and Tracking
    gps_device_id = db.Column(db.String(50), unique=True)
    last_maintenance_date = db.Column(db.DateTime)
    next_maintenance_due = db.Column(db.DateTime)
    
    # Relationships
    current_route = db.relationship('Route', backref='assigned_buses', lazy=True)
    current_driver = db.relationship('Driver', backref='assigned_bus', lazy=True)
    gps_locations = db.relationship('GPSLocation', backref='bus', lazy=True, cascade='all, delete-orphan')
    bus_statuses = db.relationship('BusStatus', backref='bus', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Bus {self.bus_number}>'
    
    def get_current_location(self):
        """Get the most recent GPS location"""
        return self.gps_locations.filter_by(is_active=True).order_by(
            db.desc('timestamp')
        ).first()
    
    def get_current_status(self):
        """Get the most recent bus status"""
        return self.bus_statuses.filter_by(is_active=True).order_by(
            db.desc('timestamp')
        ).first()
    
    def get_available_seats(self):
        """Calculate available seats based on current reservations"""
        from .passenger import SeatReservation
        current_reservations = SeatReservation.query.filter_by(
            bus_id=self.id,
            status='ACTIVE',
            is_active=True
        ).count()
        return max(0, self.total_seats - current_reservations)
    
    def is_maintenance_due(self):
        """Check if maintenance is due"""
        if not self.next_maintenance_due:
            return False
        return datetime.utcnow() >= self.next_maintenance_due
    
    def to_dict(self):
        """Convert to dictionary with additional computed fields"""
        data = super().to_dict()
        data['available_seats'] = self.get_available_seats()
        data['maintenance_due'] = self.is_maintenance_due()
        
        current_location = self.get_current_location()
        if current_location:
            data['current_location'] = current_location.to_dict()
        
        current_status = self.get_current_status()
        if current_status:
            data['current_bus_status'] = current_status.to_dict()
            
        return data
