from flask import Blueprint, request, jsonify
from ..models.alert import <PERSON><PERSON>, EmergencyAlert
from datetime import datetime

alerts_bp = Blueprint('alerts', __name__)

@alerts_bp.route('/', methods=['GET'])
def get_active_alerts():
    """Get all active alerts"""
    try:
        alerts = Alert.query.filter_by(status='ACTIVE', is_active=True).all()
        active_alerts = [alert for alert in alerts if alert.is_active()]
        
        return jsonify({
            'success': True,
            'data': [alert.to_dict() for alert in active_alerts],
            'count': len(active_alerts)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@alerts_bp.route('/', methods=['POST'])
def create_alert():
    """Create a new alert"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['title', 'message', 'alert_type']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400
        
        alert = Alert(
            title=data['title'],
            message=data['message'],
            alert_type=data['alert_type'],
            severity=data.get('severity', 'MEDIUM'),
            target_audience=data.get('target_audience', 'ALL'),
            route_ids=data.get('route_ids'),
            stop_ids=data.get('stop_ids'),
            bus_ids=data.get('bus_ids'),
            end_time=datetime.fromisoformat(data['end_time']) if data.get('end_time') else None,
            action_required=data.get('action_required', False),
            external_link=data.get('external_link'),
            contact_info=data.get('contact_info'),
            created_by=data.get('created_by'),
            source=data.get('source', 'MANUAL')
        )
        
        alert.save()
        
        return jsonify({
            'success': True,
            'data': alert.to_dict(),
            'message': 'Alert created successfully'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@alerts_bp.route('/<alert_id>/resolve', methods=['POST'])
def resolve_alert(alert_id):
    """Resolve an alert"""
    try:
        alert = Alert.get_by_id(alert_id)
        if not alert:
            return jsonify({'success': False, 'error': 'Alert not found'}), 404
        
        data = request.get_json() or {}
        resolved_by = data.get('resolved_by')
        
        alert.resolve(resolved_by)
        
        return jsonify({
            'success': True,
            'message': 'Alert resolved successfully'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@alerts_bp.route('/emergency', methods=['GET'])
def get_emergency_alerts():
    """Get all active emergency alerts"""
    try:
        emergency_alerts = EmergencyAlert.query.filter_by(
            status='ACTIVE',
            is_active=True
        ).order_by(EmergencyAlert.severity_level.desc()).all()
        
        return jsonify({
            'success': True,
            'data': [alert.to_dict() for alert in emergency_alerts],
            'count': len(emergency_alerts)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@alerts_bp.route('/emergency', methods=['POST'])
def create_emergency_alert():
    """Create a new emergency alert"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['emergency_type', 'description', 'severity_level']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400
        
        emergency_alert = EmergencyAlert(
            emergency_type=data['emergency_type'],
            description=data['description'],
            severity_level=data['severity_level'],
            bus_id=data.get('bus_id'),
            route_id=data.get('route_id'),
            stop_id=data.get('stop_id'),
            latitude=data.get('latitude'),
            longitude=data.get('longitude'),
            location_description=data.get('location_description'),
            driver_id=data.get('driver_id'),
            passenger_count=data.get('passenger_count', 0),
            injuries_reported=data.get('injuries_reported', False),
            casualties_count=data.get('casualties_count', 0),
            reporter_name=data.get('reporter_name'),
            reporter_phone=data.get('reporter_phone'),
            requires_investigation=data.get('requires_investigation', False),
            insurance_claim_needed=data.get('insurance_claim_needed', False)
        )
        
        # Generate incident number
        emergency_alert.generate_incident_number()
        emergency_alert.save()
        
        # Auto-notify emergency services for critical alerts
        if emergency_alert.is_critical():
            emergency_alert.notify_emergency_services()
        
        return jsonify({
            'success': True,
            'data': emergency_alert.to_dict(),
            'message': 'Emergency alert created successfully'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@alerts_bp.route('/emergency/<alert_id>/resolve', methods=['POST'])
def resolve_emergency_alert(alert_id):
    """Resolve an emergency alert"""
    try:
        emergency_alert = EmergencyAlert.get_by_id(alert_id)
        if not emergency_alert:
            return jsonify({'success': False, 'error': 'Emergency alert not found'}), 404
        
        data = request.get_json() or {}
        resolution_notes = data.get('resolution_notes')
        
        emergency_alert.resolve_emergency(resolution_notes)
        
        return jsonify({
            'success': True,
            'message': 'Emergency alert resolved successfully'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@alerts_bp.route('/route/<route_id>', methods=['GET'])
def get_route_alerts(route_id):
    """Get alerts affecting a specific route"""
    try:
        alerts = Alert.query.filter_by(status='ACTIVE', is_active=True).all()
        route_alerts = [alert for alert in alerts if alert.is_active() and alert.affects_route(route_id)]
        
        return jsonify({
            'success': True,
            'data': [alert.to_dict() for alert in route_alerts],
            'count': len(route_alerts)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@alerts_bp.route('/stop/<stop_id>', methods=['GET'])
def get_stop_alerts(stop_id):
    """Get alerts affecting a specific stop"""
    try:
        alerts = Alert.query.filter_by(status='ACTIVE', is_active=True).all()
        stop_alerts = [alert for alert in alerts if alert.is_active() and alert.affects_stop(stop_id)]
        
        return jsonify({
            'success': True,
            'data': [alert.to_dict() for alert in stop_alerts],
            'count': len(stop_alerts)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
