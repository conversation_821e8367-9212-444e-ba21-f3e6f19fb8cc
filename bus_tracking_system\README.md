# GPS-Based Government Bus Tracking System

## Overview
A comprehensive real-time GPS tracking system for government buses that provides passengers with accurate arrival updates, seat availability, and route information while ensuring safety and convenience.

## System Architecture

### Core Components
1. **GPS Tracking Service** - Real-time location capture and processing
2. **Data Management Layer** - Database operations and data synchronization
3. **Passenger Information API** - REST endpoints for passenger services
4. **Web Dashboard** - User interface for passengers and administrators
5. **Safety & Security Module** - Emergency alerts and monitoring
6. **Admin Management Interface** - System administration and monitoring

### Technology Stack
- **Backend**: Python (Flask/FastAPI)
- **Database**: SQLite (development) / PostgreSQL (production)
- **Real-time Communication**: WebSocket/Socket.IO
- **Frontend**: HTML5, CSS3, JavaScript
- **GPS Integration**: GPS simulation and real device support
- **Mapping**: OpenStreetMap/Leaflet.js

## Features

### For Passengers
- Real-time bus location tracking
- Accurate arrival time predictions
- Seat availability information
- Route information (start/end points, stops)
- Emergency alerts and notifications
- Multi-language support

### For Administrators
- Fleet management dashboard
- Route planning and optimization
- Driver management and authentication
- System health monitoring
- Analytics and reporting

### Safety Features
- Emergency alert system
- Driver authentication
- Passenger safety monitoring
- Real-time incident reporting
- Automated safety notifications

## Project Structure
```
bus_tracking_system/
├── backend/
│   ├── models/          # Data models
│   ├── services/        # Business logic
│   ├── api/            # REST API endpoints
│   ├── utils/          # Utility functions
│   └── config/         # Configuration files
├── frontend/
│   ├── static/         # CSS, JS, images
│   ├── templates/      # HTML templates
│   └── components/     # Reusable components
├── database/
│   ├── migrations/     # Database migrations
│   └── seeds/          # Sample data
├── tests/              # Test suites
└── docs/               # Documentation
```

## Installation and Setup

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)
- Modern web browser with JavaScript enabled

### Quick Start
1. **Clone or download the project**
   ```bash
   cd bus_tracking_system
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Access the system**
   - Web Dashboard: `http://localhost:5000`
   - API Documentation: `http://localhost:5000/api`
   - Health Check: `http://localhost:5000/health`

5. **Run the demo (optional)**
   ```bash
   python demo.py
   ```

### Detailed Setup

#### 1. Environment Setup
```bash
# Create virtual environment (recommended)
python -m venv bus_tracking_env
source bus_tracking_env/bin/activate  # On Windows: bus_tracking_env\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

#### 2. Database Initialization
The application automatically creates and initializes the SQLite database with sample data on first run.

#### 3. Configuration
Edit `backend/config/config.py` to customize:
- Database settings
- GPS update intervals
- API keys for external services
- CORS settings

#### 4. Running in Production
For production deployment:
```bash
# Set environment
export FLASK_ENV=production

# Use PostgreSQL database
export DATABASE_URL=postgresql://user:password@localhost/bus_tracking

# Run with gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## Key Features Implemented

### ✅ Real-time GPS Tracking
- Live bus location updates every 30 seconds
- GPS simulation for testing and demonstration
- Historical location tracking
- Speed and heading calculation

### ✅ Passenger Information System
- Real-time arrival predictions
- Seat availability tracking
- Route information with stops
- Search functionality for routes and stops

### ✅ Safety and Security
- Emergency alert system
- Driver authentication
- Passenger safety monitoring
- Incident reporting and tracking

### ✅ Web Dashboard
- Interactive map with live bus locations
- Real-time arrival information
- Route planning and search
- Mobile-responsive design

### ✅ API Endpoints
- **Buses**: `/api/buses/` - Bus information and status
- **Routes**: `/api/routes/` - Route details and stops
- **Stops**: `/api/stops/` - Bus stop information
- **Tracking**: `/api/tracking/` - Real-time GPS data
- **Passengers**: `/api/passengers/` - Passenger services
- **Alerts**: `/api/alerts/` - Emergency and safety alerts

## Demo Features

The system includes a comprehensive demo script (`demo.py`) that showcases:
- System health monitoring
- Bus tracking and simulation
- Route and stop information
- Live tracking capabilities
- Alert system functionality
- Search capabilities

Run the demo with: `python demo.py`

## Contributing
Please read CONTRIBUTING.md for details on our code of conduct and the process for submitting pull requests.

## License
This project is licensed under the MIT License - see the LICENSE file for details.
