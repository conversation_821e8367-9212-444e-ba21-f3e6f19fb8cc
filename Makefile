# Advanced Design Project Platform Makefile
# Provides convenient commands for development, testing, and deployment

.PHONY: help install dev test build deploy clean lint format security

# Default target
help: ## Show this help message
	@echo "Advanced Design Project Platform"
	@echo "================================"
	@echo ""
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development Environment
install: ## Install all dependencies
	@echo "Installing dependencies..."
	cd bus_tracking_system && pip install -r requirements.txt
	cd "translator tool with login" && npm install
	cd "translator tool with login/backend" && pip install -r requirements.txt

dev: ## Start development environment
	@echo "Starting development environment..."
	docker-compose up -d
	@echo "Services available at:"
	@echo "  Bus Tracking API: http://localhost:8000"
	@echo "  Translation API: http://localhost:8001"
	@echo "  Translation Frontend: http://localhost:3000"
	@echo "  Grafana: http://localhost:3000"
	@echo "  Prometheus: http://localhost:9090"

dev-stop: ## Stop development environment
	@echo "Stopping development environment..."
	docker-compose down

dev-logs: ## Show development logs
	docker-compose logs -f

dev-clean: ## Clean development environment
	docker-compose down -v
	docker system prune -f

# Frontend Development
frontend-dev: ## Start frontend development server
	cd "translator tool with login" && npm start

frontend-build: ## Build frontend for production
	cd "translator tool with login" && npm run build

frontend-test: ## Run frontend tests
	cd "translator tool with login" && npm test -- --coverage

# Backend Development
backend-dev-bus: ## Start bus tracking API development server
	cd bus_tracking_system && uvicorn main:app --reload --host 0.0.0.0 --port 8000

backend-dev-translator: ## Start translator API development server
	cd "translator tool with login/backend" && uvicorn main:app --reload --host 0.0.0.0 --port 8001

backend-test-bus: ## Run bus tracking API tests
	cd bus_tracking_system && pytest --cov=. --cov-report=html --cov-report=term

backend-test-translator: ## Run translator API tests
	cd "translator tool with login/backend" && pytest --cov=. --cov-report=html --cov-report=term

# Testing
test: ## Run all tests
	@echo "Running all tests..."
	$(MAKE) backend-test-bus
	$(MAKE) backend-test-translator
	$(MAKE) frontend-test

test-integration: ## Run integration tests
	@echo "Running integration tests..."
	docker-compose -f docker-compose.test.yml up --abort-on-container-exit
	docker-compose -f docker-compose.test.yml down

test-performance: ## Run performance tests
	@echo "Running performance tests..."
	k6 run tests/performance/load-test.js

# Code Quality
lint: ## Run linting on all code
	@echo "Running linters..."
	cd bus_tracking_system && flake8 . --max-line-length=88 --extend-ignore=E203,W503
	cd bus_tracking_system && mypy . --ignore-missing-imports
	cd "translator tool with login/backend" && flake8 . --max-line-length=88 --extend-ignore=E203,W503
	cd "translator tool with login" && npm run lint

format: ## Format all code
	@echo "Formatting code..."
	cd bus_tracking_system && black . && isort .
	cd "translator tool with login/backend" && black . && isort .
	cd "translator tool with login" && npm run format

security: ## Run security checks
	@echo "Running security checks..."
	cd bus_tracking_system && bandit -r . -f json -o bandit-report.json
	cd bus_tracking_system && safety check --json --output safety-report.json
	cd "translator tool with login/backend" && bandit -r . -f json -o bandit-report.json
	cd "translator tool with login" && npm audit

# Building
build: ## Build all Docker images
	@echo "Building Docker images..."
	docker build -t bus-tracking:latest ./bus_tracking_system
	docker build -t translator-api:latest ./translator\ tool\ with\ login/backend
	docker build -t translator-frontend:latest ./translator\ tool\ with\ login

build-push: ## Build and push Docker images
	@echo "Building and pushing Docker images..."
	docker build -t ghcr.io/$(GITHUB_REPOSITORY)/bus-tracking:$(VERSION) ./bus_tracking_system
	docker build -t ghcr.io/$(GITHUB_REPOSITORY)/translator-api:$(VERSION) ./translator\ tool\ with\ login/backend
	docker build -t ghcr.io/$(GITHUB_REPOSITORY)/translator-frontend:$(VERSION) ./translator\ tool\ with\ login
	docker push ghcr.io/$(GITHUB_REPOSITORY)/bus-tracking:$(VERSION)
	docker push ghcr.io/$(GITHUB_REPOSITORY)/translator-api:$(VERSION)
	docker push ghcr.io/$(GITHUB_REPOSITORY)/translator-frontend:$(VERSION)

# Database
db-migrate: ## Run database migrations
	@echo "Running database migrations..."
	cd bus_tracking_system && alembic upgrade head
	cd "translator tool with login/backend" && alembic upgrade head

db-reset: ## Reset databases
	@echo "Resetting databases..."
	docker-compose exec postgres psql -U postgres -c "DROP DATABASE IF EXISTS bus_tracking;"
	docker-compose exec postgres psql -U postgres -c "DROP DATABASE IF EXISTS translator;"
	docker-compose exec postgres psql -U postgres -c "CREATE DATABASE bus_tracking;"
	docker-compose exec postgres psql -U postgres -c "CREATE DATABASE translator;"
	$(MAKE) db-migrate

# Kubernetes Deployment
k8s-deploy-staging: ## Deploy to Kubernetes staging
	@echo "Deploying to staging..."
	kubectl apply -f k8s/namespace.yaml
	kubectl apply -f k8s/ -n advanced-platform-dev
	kubectl rollout status deployment/bus-tracking-api -n advanced-platform-dev
	kubectl rollout status deployment/translator-api -n advanced-platform-dev
	kubectl rollout status deployment/translator-frontend -n advanced-platform-dev

k8s-deploy-prod: ## Deploy to Kubernetes production
	@echo "Deploying to production..."
	kubectl apply -f k8s/namespace.yaml
	kubectl apply -f k8s/ -n advanced-platform
	kubectl rollout status deployment/bus-tracking-api -n advanced-platform
	kubectl rollout status deployment/translator-api -n advanced-platform
	kubectl rollout status deployment/translator-frontend -n advanced-platform

k8s-status: ## Check Kubernetes deployment status
	@echo "Checking deployment status..."
	kubectl get pods -n advanced-platform
	kubectl get services -n advanced-platform
	kubectl get ingress -n advanced-platform

k8s-logs: ## Show Kubernetes logs
	kubectl logs -f deployment/bus-tracking-api -n advanced-platform
	kubectl logs -f deployment/translator-api -n advanced-platform

# Monitoring
monitoring-setup: ## Set up monitoring stack
	@echo "Setting up monitoring..."
	kubectl apply -f monitoring/prometheus.yml
	kubectl apply -f monitoring/grafana.yml
	kubectl apply -f monitoring/alertmanager.yml

monitoring-status: ## Check monitoring status
	kubectl get pods -n monitoring
	kubectl get services -n monitoring

# Utilities
clean: ## Clean up temporary files and caches
	@echo "Cleaning up..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name ".pytest_cache" -delete
	find . -type d -name "node_modules" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name ".coverage" -delete
	find . -type d -name "htmlcov" -delete
	docker system prune -f

docs: ## Generate documentation
	@echo "Generating documentation..."
	cd bus_tracking_system && sphinx-build -b html docs docs/_build
	cd "translator tool with login" && npm run docs

backup: ## Create backup of databases
	@echo "Creating database backup..."
	docker-compose exec postgres pg_dump -U postgres bus_tracking > backup_bus_tracking_$(shell date +%Y%m%d_%H%M%S).sql
	docker-compose exec postgres pg_dump -U postgres translator > backup_translator_$(shell date +%Y%m%d_%H%M%S).sql

restore: ## Restore database from backup (requires BACKUP_FILE variable)
	@echo "Restoring database from $(BACKUP_FILE)..."
	docker-compose exec -T postgres psql -U postgres -d bus_tracking < $(BACKUP_FILE)

# Environment setup
setup-dev: ## Set up development environment from scratch
	@echo "Setting up development environment..."
	$(MAKE) install
	$(MAKE) dev
	sleep 30
	$(MAKE) db-migrate
	@echo "Development environment ready!"

setup-prod: ## Set up production environment
	@echo "Setting up production environment..."
	$(MAKE) k8s-deploy-prod
	$(MAKE) monitoring-setup
	@echo "Production environment ready!"

# Health checks
health: ## Check health of all services
	@echo "Checking service health..."
	curl -f http://localhost:8000/health || echo "Bus Tracking API: DOWN"
	curl -f http://localhost:8001/health || echo "Translator API: DOWN"
	curl -f http://localhost:3000 || echo "Frontend: DOWN"

# Version management
version: ## Show current version
	@echo "Current version: $(shell git describe --tags --always)"

tag: ## Create a new version tag (requires VERSION variable)
	@echo "Creating version tag $(VERSION)..."
	git tag -a $(VERSION) -m "Release $(VERSION)"
	git push origin $(VERSION)

# Default values
VERSION ?= $(shell git describe --tags --always)
GITHUB_REPOSITORY ?= your-org/advanced-design-project
