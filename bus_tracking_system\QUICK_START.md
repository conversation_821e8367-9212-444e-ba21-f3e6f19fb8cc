# 🚌 Quick Start Guide - GPS Bus Tracking System

## 🎯 Two Ways to Run the Project

### Option 1: Simple Demo (No Dependencies Required)
**Perfect for quick testing and demonstration**

1. **Double-click** `run_demo.bat` (Windows) or run:
   ```bash
   python simple_demo.py
   ```

2. **Open your browser** and go to: `http://localhost:8000`

3. **Features Available:**
   - ✅ Live bus tracking with GPS simulation
   - ✅ Route information
   - ✅ Bus stop details
   - ✅ Real-time arrival predictions
   - ✅ Service alerts
   - ✅ Interactive web interface

### Option 2: Full System (All Features)
**Complete implementation with all advanced features**

1. **Install Python 3.8+** from https://python.org
   - ⚠️ Make sure to check "Add Python to PATH" during installation

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the full application:**
   ```bash
   python app.py
   ```

4. **Access the system:**
   - Web Dashboard: `http://localhost:5000`
   - API Documentation: `http://localhost:5000/api`

## 🎮 Demo Features

### Real-time Bus Tracking
- **Live GPS Updates**: Buses move every 30 seconds with simulated GPS data
- **Speed Monitoring**: Real-time speed and location tracking
- **Route Following**: Buses follow predefined routes with multiple stops

### Passenger Information
- **Arrival Predictions**: Real-time ETAs for buses at each stop
- **Seat Availability**: Live seat count updates
- **Route Search**: Find routes and stops easily

### Interactive Web Interface
- **Live Data**: Auto-refreshing bus locations every 30 seconds
- **Multiple Views**: Buses, routes, stops, arrivals, and alerts
- **Mobile Friendly**: Responsive design works on all devices

## 🔧 Troubleshooting

### Python Not Found?
1. Download Python from https://python.org
2. During installation, check "Add Python to PATH"
3. Restart your command prompt/terminal
4. Test with: `python --version`

### Port Already in Use?
- Simple Demo uses port 8000
- Full System uses port 5000
- Make sure no other applications are using these ports

### Dependencies Issues?
For the simple demo, no dependencies are required!
For the full system, run: `pip install -r requirements.txt`

## 📱 How to Use the Demo

1. **Start the Server**: Run `python simple_demo.py`
2. **Open Browser**: Go to `http://localhost:8000`
3. **Explore Features**:
   - Click "Refresh Bus Data" to see live bus locations
   - Click "Load Routes" to see available routes
   - Click "Load Stops" to see bus stop information
   - Click stop buttons to see arrival predictions
   - Click "Load Alerts" to see service notifications

## 🌟 Key Demo Highlights

### Live GPS Simulation
- 2 buses (BUS001, BUS002) moving on Route R001
- Real-time location updates every 30 seconds
- Speed simulation between 20-40 km/h
- Automatic coordinate updates

### Route Information
- **Route R001**: Central Station → Airport Road
- **5 Bus Stops**: Central Station, City Mall, Hospital Junction, University Gate, Airport Road
- **15.5 km total distance**, estimated 45 minutes duration
- **₹15 base fare**

### Smart Arrivals
- Dynamic arrival predictions (2-15 minutes)
- Seat availability information
- Multiple buses per route

### Service Alerts
- Real-time service notifications
- Different alert types (INFO, WARNING, EMERGENCY)
- Severity levels and timestamps

## 🚀 Next Steps

After trying the demo:
1. **Explore the API**: Visit the endpoints directly in your browser
2. **Check the Code**: Look at `simple_demo.py` to understand the implementation
3. **Run Full System**: Try `python app.py` for the complete feature set
4. **Customize**: Modify the sample data to test different scenarios

## 📞 Support

If you encounter any issues:
1. Make sure Python is installed and in PATH
2. Check that ports 8000/5000 are available
3. Try the simple demo first before the full system
4. Look at the console output for error messages

---

**🎉 Enjoy exploring the GPS-Based Bus Tracking System!**
