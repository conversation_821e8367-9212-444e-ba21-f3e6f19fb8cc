// app.js
const pdfInput = document.getElementById('pdfInput');
const pdfText = document.getElementById('input');
const pdfjsLib = window['pdfjs-dist/build/pdf'];

pdfInput.addEventListener('change', handlePdfUpload);

function handlePdfUpload(event) {
    const file = event.target.files[0];
    if (file && file.type === 'application/pdf') {
        const reader = new FileReader();
        reader.onload = async () => {
            const buffer = reader.result;
            await extractTextFromPdf(buffer);
        };
        reader.readAsArrayBuffer(file);
    } else {
        pdfText.textContent = 'Please select a valid PDF file.';
    }
}

async function extractTextFromPdf(buffer) {
    const pdf = await pdfjsLib.getDocument(buffer).promise;
    let fullText = '';

    for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();
        const pageText = textContent.items.map(item => item.str).join(' ');
        fullText += pageText + '\n';
    }

    pdfText.textContent = fullText;
    console.log(pdfText.textContent)
}
