groups:
  - name: platform.rules
    rules:
      # High-level service availability
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
          description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: |
          (
            rate(http_requests_total{status=~"5.."}[5m]) /
            rate(http_requests_total[5m])
          ) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate on {{ $labels.instance }}"
          description: "Error rate is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      - alert: HighLatency
        expr: |
          histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High latency on {{ $labels.instance }}"
          description: "95th percentile latency is {{ $value }}s on {{ $labels.instance }}"

  - name: infrastructure.rules
    rules:
      # CPU and Memory
      - alert: HighCPUUsage
        expr: |
          (
            100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
          ) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: |
          (
            (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / 
            node_memory_MemTotal_bytes
          ) > 0.85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      - alert: DiskSpaceLow
        expr: |
          (
            (node_filesystem_size_bytes - node_filesystem_free_bytes) /
            node_filesystem_size_bytes
          ) > 0.85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low disk space on {{ $labels.instance }}"
          description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.instance }} mount {{ $labels.mountpoint }}"

  - name: kubernetes.rules
    rules:
      # Pod and container alerts
      - alert: PodCrashLooping
        expr: |
          rate(kube_pod_container_status_restarts_total[15m]) > 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Pod {{ $labels.pod }} is crash looping"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is restarting frequently"

      - alert: PodNotReady
        expr: |
          kube_pod_status_ready{condition="false"} == 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Pod {{ $labels.pod }} not ready"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} has been not ready for more than 5 minutes"

      - alert: DeploymentReplicasMismatch
        expr: |
          kube_deployment_spec_replicas != kube_deployment_status_available_replicas
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Deployment {{ $labels.deployment }} has mismatched replicas"
          description: "Deployment {{ $labels.deployment }} in namespace {{ $labels.namespace }} has {{ $labels.spec_replicas }} desired but {{ $labels.available_replicas }} available"

  - name: database.rules
    rules:
      # PostgreSQL alerts
      - alert: PostgreSQLDown
        expr: pg_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL instance {{ $labels.instance }} is down"

      - alert: PostgreSQLTooManyConnections
        expr: |
          pg_stat_activity_count / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL has too many connections"
          description: "PostgreSQL instance {{ $labels.instance }} has {{ $value | humanizePercentage }} connections used"

      - alert: PostgreSQLSlowQueries
        expr: |
          pg_stat_activity_max_tx_duration > 300
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL has slow queries"
          description: "PostgreSQL instance {{ $labels.instance }} has queries running for more than 5 minutes"

      # Redis alerts
      - alert: RedisDown
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis instance {{ $labels.instance }} is down"

      - alert: RedisHighMemoryUsage
        expr: |
          redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis memory usage is high"
          description: "Redis instance {{ $labels.instance }} memory usage is {{ $value | humanizePercentage }}"

  - name: application.rules
    rules:
      # Bus Tracking specific alerts
      - alert: BusTrackingAPIDown
        expr: |
          up{job="bus-tracking-api"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Bus Tracking API is down"
          description: "Bus Tracking API instance {{ $labels.instance }} is down"

      - alert: GPSUpdateDelay
        expr: |
          time() - bus_tracking_last_gps_update_timestamp > 300
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "GPS updates are delayed"
          description: "GPS updates for bus {{ $labels.bus_id }} are delayed by {{ $value }}s"

      # Translator specific alerts
      - alert: TranslatorAPIDown
        expr: |
          up{job="translator-api"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Translator API is down"
          description: "Translator API instance {{ $labels.instance }} is down"

      - alert: TranslationQueueBacklog
        expr: |
          translation_queue_size > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Translation queue has backlog"
          description: "Translation queue has {{ $value }} pending translations"

      - alert: MLModelLoadFailure
        expr: |
          ml_model_load_failures_total > 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "ML model failed to load"
          description: "ML model {{ $labels.model_name }} failed to load on {{ $labels.instance }}"

  - name: security.rules
    rules:
      # Security alerts
      - alert: HighFailedLoginAttempts
        expr: |
          rate(auth_failed_login_attempts_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High number of failed login attempts"
          description: "{{ $value }} failed login attempts per second from {{ $labels.source_ip }}"

      - alert: SuspiciousAPIActivity
        expr: |
          rate(http_requests_total{status="403"}[5m]) > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Suspicious API activity detected"
          description: "High rate of 403 responses: {{ $value }} per second on {{ $labels.instance }}"

      - alert: CertificateExpiringSoon
        expr: |
          (probe_ssl_earliest_cert_expiry - time()) / 86400 < 30
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "SSL certificate expiring soon"
          description: "SSL certificate for {{ $labels.instance }} expires in {{ $value }} days"

  - name: business.rules
    rules:
      # Business logic alerts
      - alert: LowTranslationAccuracy
        expr: |
          avg_over_time(translation_confidence_score[1h]) < 0.7
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Translation accuracy is low"
          description: "Average translation confidence is {{ $value }} over the last hour"

      - alert: HighBusDelayRate
        expr: |
          (
            sum(rate(bus_delay_total[5m])) /
            sum(rate(bus_arrivals_total[5m]))
          ) > 0.3
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High bus delay rate"
          description: "{{ $value | humanizePercentage }} of buses are delayed"

      - alert: UnusualTrafficPattern
        expr: |
          abs(
            rate(http_requests_total[5m]) -
            avg_over_time(rate(http_requests_total[5m])[1d:5m] offset 1w)
          ) > 2 * stddev_over_time(rate(http_requests_total[5m])[1d:5m] offset 1w)
        for: 10m
        labels:
          severity: info
        annotations:
          summary: "Unusual traffic pattern detected"
          description: "Current request rate {{ $value }} deviates significantly from historical patterns"
