#!/usr/bin/env python3
"""
Demo script for the GPS-Based Bus Tracking System
This script demonstrates the key features of the system
"""

import requests
import json
import time
from datetime import datetime, timedelta

class BusTrackingDemo:
    def __init__(self, base_url='http://localhost:5000'):
        self.base_url = base_url
        self.api_url = f"{base_url}/api"
        
    def print_header(self, title):
        print("\n" + "="*60)
        print(f" {title}")
        print("="*60)
    
    def print_section(self, title):
        print(f"\n--- {title} ---")
    
    def make_request(self, endpoint, method='GET', data=None):
        """Make HTTP request to API"""
        url = f"{self.api_url}{endpoint}"
        
        try:
            if method == 'GET':
                response = requests.get(url)
            elif method == 'POST':
                response = requests.post(url, json=data)
            
            response.raise_for_status()
            return response.json()
        
        except requests.exceptions.RequestException as e:
            print(f"Error making request to {url}: {e}")
            return None
    
    def demo_system_status(self):
        """Demo: Check system status"""
        self.print_header("SYSTEM STATUS CHECK")
        
        # Check API health
        health = self.make_request('/health', 'GET')
        if health:
            print(f"✅ System Status: {health.get('status', 'Unknown')}")
            print(f"✅ Database: {health.get('database', 'Unknown')}")
            print(f"✅ Timestamp: {health.get('timestamp', 'Unknown')}")
        else:
            print("❌ System health check failed")
    
    def demo_buses(self):
        """Demo: Bus operations"""
        self.print_header("BUS TRACKING DEMONSTRATION")
        
        # Get all buses
        self.print_section("All Buses")
        buses = self.make_request('/buses/')
        if buses and buses.get('success'):
            print(f"Found {buses['count']} buses:")
            for bus in buses['data'][:3]:  # Show first 3
                print(f"  🚌 {bus['bus_number']} - {bus['model']} ({bus['status']})")
                print(f"     Seats: {bus['available_seats']}/{bus['total_seats']} available")
        
        # Get specific bus details
        if buses and buses['data']:
            bus_id = buses['data'][0]['id']
            bus_number = buses['data'][0]['bus_number']
            
            self.print_section(f"Bus {bus_number} Details")
            bus_detail = self.make_request(f'/buses/{bus_id}')
            if bus_detail and bus_detail.get('success'):
                bus = bus_detail['data']
                print(f"  Bus Number: {bus['bus_number']}")
                print(f"  License Plate: {bus['license_plate']}")
                print(f"  Model: {bus['manufacturer']} {bus['model']} ({bus['year']})")
                print(f"  Status: {bus['status']}")
                print(f"  Available Seats: {bus['available_seats']}/{bus['total_seats']}")
            
            # Start GPS simulation
            self.print_section(f"Starting GPS Simulation for Bus {bus_number}")
            sim_result = self.make_request(f'/buses/{bus_id}/simulation/start', 'POST', {'speed_kmh': 25})
            if sim_result and sim_result.get('success'):
                print(f"✅ {sim_result['message']}")
                
                # Wait a bit and check location
                print("⏳ Waiting 5 seconds for GPS updates...")
                time.sleep(5)
                
                location = self.make_request(f'/buses/{bus_id}/location')
                if location and location.get('success'):
                    loc_data = location['data']
                    print(f"📍 Current Location: ({loc_data['latitude']:.6f}, {loc_data['longitude']:.6f})")
                    print(f"🚀 Speed: {loc_data['speed']} km/h")
                    print(f"🕒 Last Update: {loc_data['timestamp']}")
    
    def demo_routes(self):
        """Demo: Route operations"""
        self.print_header("ROUTE INFORMATION")
        
        # Get all routes
        routes = self.make_request('/routes/')
        if routes and routes.get('success'):
            print(f"Found {routes['count']} routes:")
            for route in routes['data']:
                print(f"  🛣️  {route['route_number']}: {route['route_name']}")
                print(f"     {route['start_location']} → {route['end_location']}")
                print(f"     Distance: {route['total_distance_km']} km, Duration: {route['estimated_duration_minutes']} min")
                print(f"     Fare: ₹{route['base_fare']}, Stops: {route['total_stops']}")
                
                # Show route schedule
                route_id = route['id']
                schedule = self.make_request(f'/routes/{route_id}/schedule')
                if schedule and schedule.get('success'):
                    sched_data = schedule['data']
                    print(f"     Schedule: {sched_data['first_bus']} - {sched_data['last_bus']} (every {sched_data['frequency_minutes']} min)")
                    if sched_data['next_buses']:
                        print(f"     Next buses: {', '.join(sched_data['next_buses'][:3])}")
                print()
    
    def demo_stops(self):
        """Demo: Bus stop operations"""
        self.print_header("BUS STOP INFORMATION")
        
        # Get all stops
        stops = self.make_request('/stops/')
        if stops and stops.get('success'):
            print(f"Found {stops['count']} bus stops:")
            for stop in stops['data']:
                print(f"  🚏 {stop['stop_code']}: {stop['stop_name']}")
                print(f"     Location: ({stop['latitude']:.6f}, {stop['longitude']:.6f})")
                
                facilities = []
                if stop.get('has_shelter'): facilities.append('Shelter')
                if stop.get('has_seating'): facilities.append('Seating')
                if stop.get('has_digital_display'): facilities.append('Digital Display')
                if stop.get('has_wheelchair_access'): facilities.append('Wheelchair Access')
                
                if facilities:
                    print(f"     Facilities: {', '.join(facilities)}")
                
                # Show next arrivals
                stop_id = stop['id']
                arrivals = self.make_request(f'/stops/{stop_id}/arrivals')
                if arrivals and arrivals.get('success') and arrivals['data']:
                    print(f"     Next arrivals:")
                    for arrival in arrivals['data'][:2]:  # Show first 2
                        print(f"       🚌 Bus {arrival['bus_number']} - {arrival['estimated_minutes']} min")
                print()
    
    def demo_live_tracking(self):
        """Demo: Live tracking"""
        self.print_header("LIVE TRACKING")
        
        tracking = self.make_request('/tracking/live')
        if tracking and tracking.get('success'):
            print(f"Live tracking data for {tracking['count']} buses:")
            for bus_data in tracking['data']:
                print(f"  🚌 Bus {bus_data['bus_number']}")
                if bus_data['location']:
                    loc = bus_data['location']
                    print(f"     📍 Location: ({loc['latitude']:.6f}, {loc['longitude']:.6f})")
                    print(f"     🚀 Speed: {loc['speed']} km/h")
                    print(f"     🕒 Last Update: {loc['timestamp']}")
                
                if bus_data['status']:
                    status = bus_data['status']
                    print(f"     📊 Status: {status['operational_status']}")
                    print(f"     👥 Passengers: {status['passenger_count']}")
                print()
    
    def demo_alerts(self):
        """Demo: Alert system"""
        self.print_header("ALERT SYSTEM")
        
        # Get active alerts
        alerts = self.make_request('/alerts/')
        if alerts and alerts.get('success'):
            if alerts['count'] > 0:
                print(f"Found {alerts['count']} active alerts:")
                for alert in alerts['data']:
                    print(f"  ⚠️  {alert['alert_type']}: {alert['title']}")
                    print(f"     {alert['message']}")
                    print(f"     Severity: {alert['severity']}")
                    print()
            else:
                print("✅ No active alerts")
        
        # Create a test alert
        self.print_section("Creating Test Alert")
        test_alert = {
            'title': 'Demo Service Alert',
            'message': 'This is a demonstration alert created by the demo script.',
            'alert_type': 'INFO',
            'severity': 'LOW',
            'target_audience': 'ALL',
            'source': 'DEMO'
        }
        
        create_result = self.make_request('/alerts/', 'POST', test_alert)
        if create_result and create_result.get('success'):
            print(f"✅ Created test alert: {create_result['data']['title']}")
    
    def demo_search(self):
        """Demo: Search functionality"""
        self.print_header("SEARCH FUNCTIONALITY")
        
        # Search routes
        self.print_section("Searching Routes")
        route_search = self.make_request('/routes/search?q=Central')
        if route_search and route_search.get('success'):
            print(f"Found {route_search['count']} routes matching 'Central':")
            for route in route_search['data']:
                print(f"  🛣️  {route['route_number']}: {route['route_name']}")
        
        # Search stops
        self.print_section("Searching Stops")
        stop_search = self.make_request('/stops/search?q=Station')
        if stop_search and stop_search.get('success'):
            print(f"Found {stop_search['count']} stops matching 'Station':")
            for stop in stop_search['data']:
                print(f"  🚏 {stop['stop_code']}: {stop['stop_name']}")
    
    def run_full_demo(self):
        """Run complete demonstration"""
        print("🚌 GPS-Based Bus Tracking System - DEMO")
        print("=" * 60)
        print("This demo showcases the key features of the bus tracking system.")
        print("Make sure the Flask application is running on http://localhost:5000")
        print()
        
        try:
            self.demo_system_status()
            self.demo_buses()
            self.demo_routes()
            self.demo_stops()
            self.demo_live_tracking()
            self.demo_alerts()
            self.demo_search()
            
            self.print_header("DEMO COMPLETED")
            print("✅ All demonstrations completed successfully!")
            print("🌐 Visit http://localhost:5000 to see the web dashboard")
            print("📚 Check the API documentation for more endpoints")
            
        except KeyboardInterrupt:
            print("\n\n⏹️  Demo interrupted by user")
        except Exception as e:
            print(f"\n\n❌ Demo failed with error: {e}")

if __name__ == '__main__':
    demo = BusTrackingDemo()
    demo.run_full_demo()
