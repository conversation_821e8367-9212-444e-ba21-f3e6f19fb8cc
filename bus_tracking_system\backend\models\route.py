from .base import BaseModel, db
from datetime import datetime, time

class Route(BaseModel):
    """Route model for storing bus route information"""
    __tablename__ = 'routes'
    
    # Basic Information
    route_number = db.Column(db.String(20), unique=True, nullable=False)
    route_name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    
    # Route Details
    start_location = db.Column(db.String(200), nullable=False)
    end_location = db.Column(db.String(200), nullable=False)
    total_distance_km = db.Column(db.Float, nullable=False)
    estimated_duration_minutes = db.Column(db.Integer, nullable=False)
    
    # Operational Information
    is_circular = db.Column(db.Boolean, default=False)
    operates_on_weekdays = db.Column(db.Boolean, default=True)
    operates_on_weekends = db.Column(db.<PERSON>, default=True)
    operates_on_holidays = db.Column(db.<PERSON>an, default=False)
    
    # Timing
    first_bus_time = db.Column(db.Time, default=time(6, 0))  # 6:00 AM
    last_bus_time = db.Column(db.Time, default=time(22, 0))  # 10:00 PM
    frequency_minutes = db.Column(db.Integer, default=15)  # Every 15 minutes
    
    # Fare Information
    base_fare = db.Column(db.Float, nullable=False, default=10.0)
    fare_per_km = db.Column(db.Float, default=1.5)
    
    # Status
    status = db.Column(db.String(20), default='ACTIVE')  # ACTIVE, SUSPENDED, MAINTENANCE
    
    # Relationships
    route_stops = db.relationship('RouteStop', backref='route', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Route {self.route_number}: {self.route_name}>'
    
    def get_stops_in_order(self):
        """Get all stops in the correct order"""
        return self.route_stops.filter_by(is_active=True).order_by('stop_order').all()
    
    def get_total_stops(self):
        """Get total number of stops"""
        return self.route_stops.filter_by(is_active=True).count()
    
    def calculate_fare(self, from_stop_order, to_stop_order):
        """Calculate fare between two stops"""
        if from_stop_order >= to_stop_order:
            return 0
        
        # Simple calculation based on number of stops
        stop_distance = abs(to_stop_order - from_stop_order)
        distance_km = (stop_distance / self.get_total_stops()) * self.total_distance_km
        return self.base_fare + (distance_km * self.fare_per_km)
    
    def get_next_bus_times(self, current_time=None):
        """Get next few bus arrival times"""
        if not current_time:
            current_time = datetime.now().time()
        
        next_times = []
        current_minutes = current_time.hour * 60 + current_time.minute
        first_bus_minutes = self.first_bus_time.hour * 60 + self.first_bus_time.minute
        last_bus_minutes = self.last_bus_time.hour * 60 + self.last_bus_time.minute
        
        # Find next bus time
        next_bus_minutes = first_bus_minutes
        while next_bus_minutes <= last_bus_minutes:
            if next_bus_minutes >= current_minutes:
                hours = next_bus_minutes // 60
                minutes = next_bus_minutes % 60
                next_times.append(time(hours, minutes))
                if len(next_times) >= 3:  # Return next 3 buses
                    break
            next_bus_minutes += self.frequency_minutes
        
        return next_times
    
    def to_dict(self):
        """Convert to dictionary with additional computed fields"""
        data = super().to_dict()
        data['total_stops'] = self.get_total_stops()
        data['stops'] = [stop.to_dict() for stop in self.get_stops_in_order()]
        data['next_bus_times'] = [t.strftime('%H:%M') for t in self.get_next_bus_times()]
        return data


class RouteStop(BaseModel):
    """Association table for routes and bus stops with additional information"""
    __tablename__ = 'route_stops'
    
    route_id = db.Column(db.String(36), db.ForeignKey('routes.id'), nullable=False)
    stop_id = db.Column(db.String(36), db.ForeignKey('bus_stops.id'), nullable=False)
    stop_order = db.Column(db.Integer, nullable=False)  # Order of stop in the route
    
    # Timing information
    estimated_arrival_time_offset = db.Column(db.Integer, default=0)  # Minutes from route start
    average_dwell_time_seconds = db.Column(db.Integer, default=30)  # Time spent at stop
    
    # Distance information
    distance_from_previous_km = db.Column(db.Float, default=0.0)
    cumulative_distance_km = db.Column(db.Float, default=0.0)
    
    # Relationships
    stop = db.relationship('BusStop', backref='route_associations', lazy=True)
    
    __table_args__ = (
        db.UniqueConstraint('route_id', 'stop_id', name='unique_route_stop'),
        db.UniqueConstraint('route_id', 'stop_order', name='unique_route_order'),
    )
    
    def __repr__(self):
        return f'<RouteStop Route:{self.route_id} Stop:{self.stop_id} Order:{self.stop_order}>'
    
    def to_dict(self):
        """Convert to dictionary with stop information"""
        data = super().to_dict()
        if self.stop:
            data['stop_info'] = self.stop.to_dict()
        return data
