#!/usr/bin/env python3
"""
Simple Demo of GPS-Based Bus Tracking System
This is a simplified version that demonstrates core concepts without external dependencies
"""

import json
import time
import random
import math
from datetime import datetime, timedelta
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

class BusTrackingSimulator:
    def __init__(self):
        # Sample data
        self.buses = {
            'BUS001': {
                'id': 'BUS001',
                'bus_number': 'BUS001',
                'license_plate': 'KA01AB1234',
                'model': 'Volvo B7RLE',
                'total_seats': 40,
                'available_seats': 25,
                'status': 'IN_SERVICE',
                'current_location': {'latitude': 12.9716, 'longitude': 77.5946, 'speed': 25, 'timestamp': datetime.now().isoformat()},
                'route_id': 'R001'
            },
            'BUS002': {
                'id': 'BUS002',
                'bus_number': 'BUS002',
                'license_plate': 'KA01AB1235',
                'model': 'Tata Starbus',
                'total_seats': 35,
                'available_seats': 18,
                'status': 'IN_SERVICE',
                'current_location': {'latitude': 12.9726, 'longitude': 77.5956, 'speed': 30, 'timestamp': datetime.now().isoformat()},
                'route_id': 'R001'
            }
        }
        
        self.routes = {
            'R001': {
                'id': 'R001',
                'route_number': 'R001',
                'route_name': 'Central - Airport Express',
                'start_location': 'Central Station',
                'end_location': 'Airport Road',
                'total_distance_km': 15.5,
                'estimated_duration_minutes': 45,
                'base_fare': 15.0,
                'stops': ['ST001', 'ST002', 'ST003', 'ST004', 'ST005']
            }
        }
        
        self.stops = {
            'ST001': {
                'id': 'ST001',
                'stop_code': 'ST001',
                'stop_name': 'Central Station',
                'latitude': 12.9716,
                'longitude': 77.5946,
                'has_shelter': True,
                'has_digital_display': True
            },
            'ST002': {
                'id': 'ST002',
                'stop_code': 'ST002',
                'stop_name': 'City Mall',
                'latitude': 12.9726,
                'longitude': 77.5956,
                'has_shelter': True,
                'has_seating': True
            },
            'ST003': {
                'id': 'ST003',
                'stop_code': 'ST003',
                'stop_name': 'Hospital Junction',
                'latitude': 12.9736,
                'longitude': 77.5966,
                'has_shelter': True,
                'has_wheelchair_access': True
            },
            'ST004': {
                'id': 'ST004',
                'stop_code': 'ST004',
                'stop_name': 'University Gate',
                'latitude': 12.9746,
                'longitude': 77.5976,
                'has_shelter': True,
                'has_digital_display': True
            },
            'ST005': {
                'id': 'ST005',
                'stop_code': 'ST005',
                'stop_name': 'Airport Road',
                'latitude': 12.9756,
                'longitude': 77.5986,
                'has_shelter': True,
                'has_seating': True
            }
        }
        
        self.alerts = [
            {
                'id': 'A001',
                'title': 'Route R001 - Minor Delay',
                'message': 'Buses on Route R001 are experiencing 5-10 minute delays due to traffic.',
                'alert_type': 'WARNING',
                'severity': 'MEDIUM',
                'timestamp': datetime.now().isoformat()
            }
        ]
        
        # Start GPS simulation
        self.start_gps_simulation()
    
    def start_gps_simulation(self):
        """Start GPS simulation in background"""
        def simulate():
            while True:
                for bus_id, bus in self.buses.items():
                    # Simulate movement
                    lat = bus['current_location']['latitude']
                    lon = bus['current_location']['longitude']
                    
                    # Add small random movement
                    lat += random.uniform(-0.001, 0.001)
                    lon += random.uniform(-0.001, 0.001)
                    speed = random.uniform(20, 40)
                    
                    bus['current_location'] = {
                        'latitude': lat,
                        'longitude': lon,
                        'speed': speed,
                        'timestamp': datetime.now().isoformat()
                    }
                
                time.sleep(30)  # Update every 30 seconds
        
        thread = threading.Thread(target=simulate, daemon=True)
        thread.start()
    
    def get_all_buses(self):
        return list(self.buses.values())
    
    def get_bus(self, bus_id):
        return self.buses.get(bus_id)
    
    def get_all_routes(self):
        return list(self.routes.values())
    
    def get_route(self, route_id):
        return self.routes.get(route_id)
    
    def get_all_stops(self):
        return list(self.stops.values())
    
    def get_stop(self, stop_id):
        return self.stops.get(stop_id)
    
    def get_stop_arrivals(self, stop_id):
        # Simulate arrivals
        arrivals = []
        for bus_id, bus in self.buses.items():
            if bus['status'] == 'IN_SERVICE':
                # Calculate estimated arrival (simplified)
                eta_minutes = random.randint(2, 15)
                arrivals.append({
                    'bus_number': bus['bus_number'],
                    'route_number': self.routes[bus['route_id']]['route_number'],
                    'estimated_minutes': eta_minutes,
                    'available_seats': bus['available_seats']
                })
        return arrivals
    
    def get_live_tracking(self):
        tracking_data = []
        for bus_id, bus in self.buses.items():
            tracking_data.append({
                'bus_id': bus_id,
                'bus_number': bus['bus_number'],
                'location': bus['current_location'],
                'status': bus['status'],
                'available_seats': bus['available_seats']
            })
        return tracking_data
    
    def get_alerts(self):
        return self.alerts

class BusTrackingHandler(BaseHTTPRequestHandler):
    simulator = BusTrackingSimulator()
    
    def do_GET(self):
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        query_params = parse_qs(parsed_path.query)
        
        # Set CORS headers
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        response_data = {'success': False, 'error': 'Not found'}
        
        try:
            if path == '/':
                # Serve simple HTML page
                self.send_response(200)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                html = self.get_html_page()
                self.wfile.write(html.encode())
                return
            
            elif path == '/api/buses':
                buses = self.simulator.get_all_buses()
                response_data = {'success': True, 'data': buses, 'count': len(buses)}
            
            elif path.startswith('/api/buses/'):
                bus_id = path.split('/')[-1]
                if path.endswith('/location'):
                    bus_id = path.split('/')[-2]
                    bus = self.simulator.get_bus(bus_id)
                    if bus:
                        response_data = {'success': True, 'data': bus['current_location']}
                else:
                    bus = self.simulator.get_bus(bus_id)
                    if bus:
                        response_data = {'success': True, 'data': bus}
            
            elif path == '/api/routes':
                routes = self.simulator.get_all_routes()
                response_data = {'success': True, 'data': routes, 'count': len(routes)}
            
            elif path.startswith('/api/routes/'):
                route_id = path.split('/')[-1]
                route = self.simulator.get_route(route_id)
                if route:
                    response_data = {'success': True, 'data': route}
            
            elif path == '/api/stops':
                stops = self.simulator.get_all_stops()
                response_data = {'success': True, 'data': stops, 'count': len(stops)}
            
            elif path.startswith('/api/stops/'):
                parts = path.split('/')
                stop_id = parts[-1]
                if path.endswith('/arrivals'):
                    stop_id = parts[-2]
                    arrivals = self.simulator.get_stop_arrivals(stop_id)
                    response_data = {'success': True, 'data': arrivals, 'count': len(arrivals)}
                else:
                    stop = self.simulator.get_stop(stop_id)
                    if stop:
                        response_data = {'success': True, 'data': stop}
            
            elif path == '/api/tracking/live':
                tracking = self.simulator.get_live_tracking()
                response_data = {'success': True, 'data': tracking, 'count': len(tracking)}
            
            elif path == '/api/alerts':
                alerts = self.simulator.get_alerts()
                response_data = {'success': True, 'data': alerts, 'count': len(alerts)}
            
            elif path == '/api/health':
                response_data = {
                    'status': 'healthy',
                    'database': 'healthy',
                    'timestamp': datetime.now().isoformat()
                }
        
        except Exception as e:
            response_data = {'success': False, 'error': str(e)}
        
        self.wfile.write(json.dumps(response_data, indent=2).encode())
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def get_html_page(self):
        return """
<!DOCTYPE html>
<html>
<head>
    <title>Bus Tracking System - Simple Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #2c3e50; margin-bottom: 30px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .bus-item, .route-item, .stop-item { padding: 10px; margin: 5px 0; background: #f8f9fa; border-radius: 5px; }
        .status { padding: 3px 8px; border-radius: 3px; color: white; font-size: 12px; }
        .in-service { background: #28a745; }
        .btn { background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .loading { text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚌 GPS-Based Bus Tracking System</h1>
            <p>Simple Demo - Real-time Bus Tracking</p>
        </div>
        
        <div class="section">
            <h3>🚌 Live Bus Tracking</h3>
            <button class="btn" onclick="loadBuses()">Refresh Bus Data</button>
            <div id="buses-data" class="loading">Click refresh to load bus data...</div>
        </div>
        
        <div class="section">
            <h3>🛣️ Routes</h3>
            <button class="btn" onclick="loadRoutes()">Load Routes</button>
            <div id="routes-data" class="loading">Click to load route data...</div>
        </div>
        
        <div class="section">
            <h3>🚏 Bus Stops</h3>
            <button class="btn" onclick="loadStops()">Load Stops</button>
            <div id="stops-data" class="loading">Click to load stop data...</div>
        </div>
        
        <div class="section">
            <h3>⏰ Next Arrivals</h3>
            <button class="btn" onclick="loadArrivals('ST001')">Central Station Arrivals</button>
            <button class="btn" onclick="loadArrivals('ST002')">City Mall Arrivals</button>
            <div id="arrivals-data" class="loading">Click a stop to see arrivals...</div>
        </div>
        
        <div class="section">
            <h3>⚠️ Service Alerts</h3>
            <button class="btn" onclick="loadAlerts()">Load Alerts</button>
            <div id="alerts-data" class="loading">Click to load alerts...</div>
        </div>
    </div>

    <script>
        async function apiCall(endpoint) {
            try {
                const response = await fetch(endpoint);
                return await response.json();
            } catch (error) {
                console.error('API call failed:', error);
                return { success: false, error: error.message };
            }
        }

        async function loadBuses() {
            document.getElementById('buses-data').innerHTML = '<div class="loading">Loading buses...</div>';
            const result = await apiCall('/api/buses');
            
            if (result.success) {
                const html = result.data.map(bus => `
                    <div class="bus-item">
                        <strong>🚌 ${bus.bus_number}</strong> - ${bus.model}
                        <span class="status in-service">${bus.status}</span><br>
                        <small>📍 Location: (${bus.current_location.latitude.toFixed(4)}, ${bus.current_location.longitude.toFixed(4)})</small><br>
                        <small>🚀 Speed: ${Math.round(bus.current_location.speed)} km/h</small><br>
                        <small>💺 Available Seats: ${bus.available_seats}/${bus.total_seats}</small>
                    </div>
                `).join('');
                document.getElementById('buses-data').innerHTML = html;
            } else {
                document.getElementById('buses-data').innerHTML = `<div style="color: red;">Error: ${result.error}</div>`;
            }
        }

        async function loadRoutes() {
            document.getElementById('routes-data').innerHTML = '<div class="loading">Loading routes...</div>';
            const result = await apiCall('/api/routes');
            
            if (result.success) {
                const html = result.data.map(route => `
                    <div class="route-item">
                        <strong>🛣️ ${route.route_number}: ${route.route_name}</strong><br>
                        <small>${route.start_location} → ${route.end_location}</small><br>
                        <small>Distance: ${route.total_distance_km} km | Duration: ${route.estimated_duration_minutes} min | Fare: ₹${route.base_fare}</small>
                    </div>
                `).join('');
                document.getElementById('routes-data').innerHTML = html;
            } else {
                document.getElementById('routes-data').innerHTML = `<div style="color: red;">Error: ${result.error}</div>`;
            }
        }

        async function loadStops() {
            document.getElementById('stops-data').innerHTML = '<div class="loading">Loading stops...</div>';
            const result = await apiCall('/api/stops');
            
            if (result.success) {
                const html = result.data.map(stop => `
                    <div class="stop-item">
                        <strong>🚏 ${stop.stop_code}: ${stop.stop_name}</strong><br>
                        <small>📍 (${stop.latitude}, ${stop.longitude})</small><br>
                        <small>Facilities: ${[
                            stop.has_shelter ? 'Shelter' : '',
                            stop.has_seating ? 'Seating' : '',
                            stop.has_digital_display ? 'Digital Display' : '',
                            stop.has_wheelchair_access ? 'Wheelchair Access' : ''
                        ].filter(f => f).join(', ') || 'Basic stop'}</small>
                    </div>
                `).join('');
                document.getElementById('stops-data').innerHTML = html;
            } else {
                document.getElementById('stops-data').innerHTML = `<div style="color: red;">Error: ${result.error}</div>`;
            }
        }

        async function loadArrivals(stopId) {
            document.getElementById('arrivals-data').innerHTML = '<div class="loading">Loading arrivals...</div>';
            const result = await apiCall(`/api/stops/${stopId}/arrivals`);
            
            if (result.success) {
                const html = result.data.map(arrival => `
                    <div class="bus-item">
                        <strong>🚌 Bus ${arrival.bus_number}</strong> (Route ${arrival.route_number})<br>
                        <small>⏰ Arriving in: ${arrival.estimated_minutes} minutes</small><br>
                        <small>💺 Available seats: ${arrival.available_seats}</small>
                    </div>
                `).join('');
                document.getElementById('arrivals-data').innerHTML = html || '<div>No upcoming arrivals</div>';
            } else {
                document.getElementById('arrivals-data').innerHTML = `<div style="color: red;">Error: ${result.error}</div>`;
            }
        }

        async function loadAlerts() {
            document.getElementById('alerts-data').innerHTML = '<div class="loading">Loading alerts...</div>';
            const result = await apiCall('/api/alerts');
            
            if (result.success) {
                const html = result.data.map(alert => `
                    <div class="bus-item">
                        <strong>⚠️ ${alert.title}</strong><br>
                        <small>${alert.message}</small><br>
                        <small>Type: ${alert.alert_type} | Severity: ${alert.severity}</small>
                    </div>
                `).join('');
                document.getElementById('alerts-data').innerHTML = html || '<div>No active alerts</div>';
            } else {
                document.getElementById('alerts-data').innerHTML = `<div style="color: red;">Error: ${result.error}</div>`;
            }
        }

        // Auto-refresh buses every 30 seconds
        setInterval(() => {
            if (document.getElementById('buses-data').innerHTML.includes('Bus')) {
                loadBuses();
            }
        }, 30000);
    </script>
</body>
</html>
        """

def run_server():
    server_address = ('', 8000)
    httpd = HTTPServer(server_address, BusTrackingHandler)
    print("🚌 GPS-Based Bus Tracking System - Simple Demo")
    print("=" * 50)
    print("✅ Server starting on http://localhost:8000")
    print("✅ GPS simulation running (buses move every 30 seconds)")
    print("✅ API endpoints available:")
    print("   - http://localhost:8000/api/buses")
    print("   - http://localhost:8000/api/routes")
    print("   - http://localhost:8000/api/stops")
    print("   - http://localhost:8000/api/tracking/live")
    print("   - http://localhost:8000/api/alerts")
    print("=" * 50)
    print("🌐 Open http://localhost:8000 in your browser")
    print("Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
