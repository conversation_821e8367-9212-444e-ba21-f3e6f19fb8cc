"""
Advanced AI Translation Service with multiple model support
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, AsyncGenerator
import structlog

import torch
from transformers import (
    AutoTokenizer, AutoModelForSeq2SeqLM, 
    pipeline, MarianMTModel, MarianTokenizer
)
import tensorflow as tf
from sentence_transformers import SentenceTransformer

from core.config import get_settings
from core.redis_client import get_cache

logger = structlog.get_logger()


class AITranslationService:
    """Advanced AI service with multiple translation models"""
    
    def __init__(self):
        self.settings = get_settings()
        self.models = {}
        self.tokenizers = {}
        self.pipelines = {}
        self.sentence_transformer = None
        self.is_initialized = False
        self.optimization_task: Optional[asyncio.Task] = None
        
        # Supported model types
        self.model_configs = {
            "neural": {
                "model_name": "Helsinki-NLP/opus-mt-en-de",
                "type": "marian"
            },
            "transformer": {
                "model_name": "facebook/m2m100_418M",
                "type": "m2m100"
            },
            "multilingual": {
                "model_name": "google/mt5-small",
                "type": "mt5"
            },
            "fast": {
                "model_name": "Helsinki-NLP/opus-mt-mul-en",
                "type": "marian"
            }
        }
        
        # Language mappings
        self.language_codes = {
            "english": "en",
            "spanish": "es", 
            "french": "fr",
            "german": "de",
            "italian": "it",
            "portuguese": "pt",
            "russian": "ru",
            "chinese": "zh",
            "japanese": "ja",
            "korean": "ko",
            "arabic": "ar",
            "hindi": "hi",
            "dutch": "nl",
            "swedish": "sv",
            "norwegian": "no",
            "danish": "da",
            "finnish": "fi",
            "polish": "pl",
            "czech": "cs",
            "hungarian": "hu",
            "romanian": "ro",
            "bulgarian": "bg",
            "croatian": "hr",
            "slovak": "sk",
            "slovenian": "sl",
            "estonian": "et",
            "latvian": "lv",
            "lithuanian": "lt",
            "greek": "el",
            "turkish": "tr",
            "hebrew": "he",
            "thai": "th",
            "vietnamese": "vi",
            "indonesian": "id",
            "malay": "ms",
            "tagalog": "tl",
            "ukrainian": "uk",
            "bengali": "bn",
            "tamil": "ta",
            "telugu": "te",
            "marathi": "mr",
            "gujarati": "gu",
            "kannada": "kn",
            "malayalam": "ml",
            "punjabi": "pa",
            "urdu": "ur",
            "persian": "fa",
            "swahili": "sw",
            "amharic": "am",
            "yoruba": "yo",
            "igbo": "ig",
            "hausa": "ha"
        }
    
    async def initialize(self) -> None:
        """Initialize AI models and services"""
        try:
            logger.info("Initializing AI translation service...")
            
            # Check if CUDA is available
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            logger.info("Using device", device=self.device)
            
            # Initialize core models
            await self.load_core_models()
            
            # Initialize sentence transformer for similarity
            await self.load_sentence_transformer()
            
            # Initialize translation pipelines
            await self.initialize_pipelines()
            
            self.is_initialized = True
            logger.info("AI translation service initialized successfully")
            
        except Exception as e:
            logger.error("AI service initialization failed", error=str(e))
            raise
    
    async def load_core_models(self) -> None:
        """Load core translation models"""
        try:
            # Load Marian models for common language pairs
            marian_models = [
                "Helsinki-NLP/opus-mt-en-de",
                "Helsinki-NLP/opus-mt-en-fr", 
                "Helsinki-NLP/opus-mt-en-es",
                "Helsinki-NLP/opus-mt-en-it",
                "Helsinki-NLP/opus-mt-de-en",
                "Helsinki-NLP/opus-mt-fr-en",
                "Helsinki-NLP/opus-mt-es-en"
            ]
            
            for model_name in marian_models:
                try:
                    tokenizer = MarianTokenizer.from_pretrained(model_name)
                    model = MarianMTModel.from_pretrained(model_name)
                    model.to(self.device)
                    
                    self.tokenizers[model_name] = tokenizer
                    self.models[model_name] = model
                    
                    logger.info("Loaded model", model=model_name)
                except Exception as e:
                    logger.warning("Failed to load model", model=model_name, error=str(e))
            
            # Load M2M100 for multilingual translation
            try:
                m2m_tokenizer = AutoTokenizer.from_pretrained("facebook/m2m100_418M")
                m2m_model = AutoModelForSeq2SeqLM.from_pretrained("facebook/m2m100_418M")
                m2m_model.to(self.device)
                
                self.tokenizers["m2m100"] = m2m_tokenizer
                self.models["m2m100"] = m2m_model
                
                logger.info("Loaded M2M100 multilingual model")
            except Exception as e:
                logger.warning("Failed to load M2M100 model", error=str(e))
                
        except Exception as e:
            logger.error("Failed to load core models", error=str(e))
            raise
    
    async def load_sentence_transformer(self) -> None:
        """Load sentence transformer for semantic similarity"""
        try:
            self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("Loaded sentence transformer model")
        except Exception as e:
            logger.warning("Failed to load sentence transformer", error=str(e))
    
    async def initialize_pipelines(self) -> None:
        """Initialize translation pipelines"""
        try:
            # Create pipelines for different model types
            for model_type, config in self.model_configs.items():
                try:
                    if config["type"] == "marian" and config["model_name"] in self.models:
                        self.pipelines[model_type] = pipeline(
                            "translation",
                            model=self.models[config["model_name"]],
                            tokenizer=self.tokenizers[config["model_name"]],
                            device=0 if self.device == "cuda" else -1
                        )
                    elif config["type"] == "m2m100" and "m2m100" in self.models:
                        self.pipelines[model_type] = pipeline(
                            "translation",
                            model=self.models["m2m100"],
                            tokenizer=self.tokenizers["m2m100"],
                            device=0 if self.device == "cuda" else -1
                        )
                    
                    logger.info("Initialized pipeline", type=model_type)
                except Exception as e:
                    logger.warning("Failed to initialize pipeline", type=model_type, error=str(e))
                    
        except Exception as e:
            logger.error("Failed to initialize pipelines", error=str(e))
    
    async def translate(
        self, 
        text: str, 
        source_lang: str, 
        target_lang: str, 
        model_type: str = "neural"
    ) -> Dict[str, Any]:
        """Translate text using specified model"""
        try:
            # Normalize language codes
            source_code = self.normalize_language_code(source_lang)
            target_code = self.normalize_language_code(target_lang)
            
            # Check cache first
            cache_key = f"translation:{hash(text)}:{source_code}:{target_code}:{model_type}"
            cache = await get_cache()
            cached_result = await cache.get(cache_key)
            
            if cached_result:
                logger.info("Translation cache hit")
                return cached_result
            
            # Detect language if auto
            if source_code == "auto":
                source_code = await self.detect_language(text)
            
            # Get appropriate model
            model_name = await self.get_best_model(source_code, target_code, model_type)
            
            if not model_name:
                # Fallback to external API or basic translation
                result = await self.fallback_translation(text, source_code, target_code)
            else:
                # Use local model
                result = await self.model_translate(text, source_code, target_code, model_name)
            
            # Add metadata
            result.update({
                "source_language": source_code,
                "target_language": target_code,
                "model_used": model_name or "fallback",
                "confidence": await self.calculate_confidence(text, result.get("translated_text", "")),
                "alternatives": await self.get_alternative_translations(text, source_code, target_code)
            })
            
            # Cache result
            await cache.set(cache_key, result, ttl=3600)  # 1 hour
            
            return result
            
        except Exception as e:
            logger.error("Translation failed", error=str(e))
            return {
                "error": str(e),
                "translated_text": text,  # Return original on error
                "source_language": source_lang,
                "target_language": target_lang
            }
    
    async def model_translate(
        self, 
        text: str, 
        source_lang: str, 
        target_lang: str, 
        model_name: str
    ) -> Dict[str, Any]:
        """Translate using specific model"""
        try:
            if model_name in self.pipelines:
                # Use pipeline
                pipeline = self.pipelines[model_name]
                
                # Prepare input based on model type
                if "m2m100" in model_name:
                    # M2M100 requires special formatting
                    tokenizer = self.tokenizers["m2m100"]
                    tokenizer.src_lang = source_lang
                    encoded = tokenizer(text, return_tensors="pt").to(self.device)
                    
                    model = self.models["m2m100"]
                    generated_tokens = model.generate(
                        **encoded,
                        forced_bos_token_id=tokenizer.get_lang_id(target_lang),
                        max_length=512
                    )
                    
                    translated_text = tokenizer.batch_decode(
                        generated_tokens, skip_special_tokens=True
                    )[0]
                else:
                    # Standard pipeline
                    result = pipeline(text, max_length=512)
                    translated_text = result[0]["translation_text"]
                
                return {
                    "translated_text": translated_text,
                    "original_text": text
                }
            else:
                raise ValueError(f"Model {model_name} not available")
                
        except Exception as e:
            logger.error("Model translation failed", model=model_name, error=str(e))
            raise
    
    async def detect_language(self, text: str) -> str:
        """Detect language of input text"""
        try:
            # Use a simple heuristic or external service
            # For now, return English as default
            return "en"
        except Exception as e:
            logger.error("Language detection failed", error=str(e))
            return "en"
    
    async def get_best_model(self, source_lang: str, target_lang: str, model_type: str) -> Optional[str]:
        """Get best available model for language pair"""
        try:
            # Check for specific language pair models
            pair_models = [
                f"Helsinki-NLP/opus-mt-{source_lang}-{target_lang}",
                f"Helsinki-NLP/opus-mt-{target_lang}-{source_lang}"
            ]
            
            for model_name in pair_models:
                if model_name in self.models:
                    return model_name
            
            # Fallback to multilingual model
            if "m2m100" in self.models:
                return "m2m100"
            
            return None
            
        except Exception as e:
            logger.error("Failed to get best model", error=str(e))
            return None
    
    async def fallback_translation(self, text: str, source_lang: str, target_lang: str) -> Dict[str, Any]:
        """Fallback translation using external API"""
        try:
            # This would integrate with external APIs like Google Translate, DeepL, etc.
            # For now, return a mock translation
            return {
                "translated_text": f"[Translated from {source_lang} to {target_lang}] {text}",
                "original_text": text,
                "fallback": True
            }
        except Exception as e:
            logger.error("Fallback translation failed", error=str(e))
            return {
                "translated_text": text,
                "original_text": text,
                "error": "Translation failed"
            }
    
    async def calculate_confidence(self, original: str, translated: str) -> float:
        """Calculate translation confidence score"""
        try:
            if not self.sentence_transformer:
                return 0.8  # Default confidence
            
            # Use sentence similarity as confidence proxy
            embeddings = self.sentence_transformer.encode([original, translated])
            similarity = torch.cosine_similarity(
                torch.tensor(embeddings[0]).unsqueeze(0),
                torch.tensor(embeddings[1]).unsqueeze(0)
            ).item()
            
            # Convert similarity to confidence (0.5-1.0 range)
            confidence = 0.5 + (similarity * 0.5)
            return min(max(confidence, 0.0), 1.0)
            
        except Exception as e:
            logger.error("Confidence calculation failed", error=str(e))
            return 0.8
    
    async def get_alternative_translations(
        self, 
        text: str, 
        source_lang: str, 
        target_lang: str
    ) -> List[Dict[str, Any]]:
        """Get alternative translations using different models"""
        try:
            alternatives = []
            
            # Try different models
            for model_type in ["neural", "transformer", "fast"]:
                try:
                    if model_type in self.pipelines:
                        result = await self.model_translate(text, source_lang, target_lang, model_type)
                        alternatives.append({
                            "text": result["translated_text"],
                            "model": model_type,
                            "confidence": await self.calculate_confidence(text, result["translated_text"])
                        })
                except Exception:
                    continue
            
            # Sort by confidence
            alternatives.sort(key=lambda x: x["confidence"], reverse=True)
            return alternatives[:3]  # Return top 3
            
        except Exception as e:
            logger.error("Failed to get alternatives", error=str(e))
            return []
    
    def normalize_language_code(self, lang: str) -> str:
        """Normalize language code"""
        lang_lower = lang.lower()
        
        # Direct code mapping
        if lang_lower in self.language_codes.values():
            return lang_lower
        
        # Name to code mapping
        if lang_lower in self.language_codes:
            return self.language_codes[lang_lower]
        
        # Default to English
        return "en"
    
    async def batch_translate(
        self, 
        texts: List[str], 
        source_lang: str, 
        target_lang: str, 
        model_type: str = "neural"
    ) -> List[Dict[str, Any]]:
        """Batch translate multiple texts"""
        try:
            results = []
            
            # Process in batches to avoid memory issues
            batch_size = 10
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                batch_results = await asyncio.gather(*[
                    self.translate(text, source_lang, target_lang, model_type)
                    for text in batch
                ])
                results.extend(batch_results)
            
            return results
            
        except Exception as e:
            logger.error("Batch translation failed", error=str(e))
            return [{"error": str(e)} for _ in texts]
    
    async def stream_translate(
        self, 
        text: str, 
        source_lang: str, 
        target_lang: str
    ) -> AsyncGenerator[str, None]:
        """Stream translation for real-time translation"""
        try:
            # Split text into sentences for streaming
            sentences = text.split('. ')
            
            for sentence in sentences:
                if sentence.strip():
                    result = await self.translate(sentence, source_lang, target_lang)
                    yield json.dumps({
                        "chunk": result.get("translated_text", ""),
                        "original": sentence,
                        "confidence": result.get("confidence", 0.8)
                    })
                    
                    # Small delay for streaming effect
                    await asyncio.sleep(0.1)
                    
        except Exception as e:
            logger.error("Stream translation failed", error=str(e))
            yield json.dumps({"error": str(e)})
    
    async def start_model_optimization(self) -> None:
        """Start background model optimization"""
        if self.optimization_task:
            return
        
        self.optimization_task = asyncio.create_task(self._optimization_loop())
        logger.info("Model optimization started")
    
    async def _optimization_loop(self) -> None:
        """Background model optimization loop"""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                
                # Optimize model performance
                await self.optimize_models()
                
            except Exception as e:
                logger.error("Optimization loop error", error=str(e))
    
    async def optimize_models(self) -> None:
        """Optimize model performance"""
        try:
            # Clear unused models from memory
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            logger.info("Model optimization completed")
            
        except Exception as e:
            logger.error("Model optimization failed", error=str(e))
    
    async def health_check(self) -> Dict[str, Any]:
        """Check AI service health"""
        return {
            "status": "healthy" if self.is_initialized else "initializing",
            "models_loaded": len(self.models),
            "pipelines_ready": len(self.pipelines),
            "device": self.device,
            "cuda_available": torch.cuda.is_available()
        }
    
    async def cleanup(self) -> None:
        """Cleanup AI service resources"""
        if self.optimization_task:
            self.optimization_task.cancel()
        
        # Clear models from memory
        self.models.clear()
        self.tokenizers.clear()
        self.pipelines.clear()
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info("AI service cleanup completed")
