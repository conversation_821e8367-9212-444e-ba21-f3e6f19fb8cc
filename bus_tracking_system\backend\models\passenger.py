from .base import BaseModel, db
from datetime import datetime

class PassengerInfo(BaseModel):
    """Passenger information for tracking and analytics"""
    __tablename__ = 'passenger_info'
    
    # Basic Information
    passenger_id = db.Column(db.String(50), unique=True)  # Could be phone number, card ID, etc.
    name = db.Column(db.String(100))
    phone_number = db.Column(db.String(20))
    email = db.Column(db.String(100))
    
    # Demographics (optional, for analytics)
    age_group = db.Column(db.String(20))  # CHILD, ADULT, SENIOR
    is_student = db.Column(db.<PERSON>olean, default=False)
    is_disabled = db.Column(db.<PERSON>an, default=False)
    
    # Preferences
    preferred_language = db.Column(db.String(10), default='en')
    notification_preferences = db.Column(db.JSON)  # SMS, email, push notifications
    
    # Travel patterns (for analytics)
    frequent_routes = db.Column(db.<PERSON>)  # List of frequently used route IDs
    home_stop_id = db.Column(db.String(36), db.<PERSON>ey('bus_stops.id'))
    work_stop_id = db.Column(db.String(36), db.ForeignKey('bus_stops.id'))
    
    # Account status
    is_verified = db.Column(db.Boolean, default=False)
    registration_date = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    home_stop = db.relationship('BusStop', foreign_keys=[home_stop_id], lazy=True)
    work_stop = db.relationship('BusStop', foreign_keys=[work_stop_id], lazy=True)
    reservations = db.relationship('SeatReservation', backref='passenger', lazy=True)
    
    def __repr__(self):
        return f'<PassengerInfo {self.passenger_id}>'
    
    def get_travel_history(self, limit=10):
        """Get recent travel history"""
        return self.reservations.filter_by(is_active=True).order_by(
            db.desc('created_at')
        ).limit(limit).all()
    
    def get_favorite_routes(self):
        """Get most frequently used routes"""
        if not self.frequent_routes:
            return []
        
        from .route import Route
        route_ids = list(self.frequent_routes.keys())
        routes = Route.query.filter(Route.id.in_(route_ids)).all()
        
        # Sort by frequency
        route_data = []
        for route in routes:
            frequency = self.frequent_routes.get(route.id, 0)
            route_data.append({
                'route': route,
                'usage_count': frequency
            })
        
        route_data.sort(key=lambda x: x['usage_count'], reverse=True)
        return route_data
    
    def to_dict(self):
        """Convert to dictionary"""
        data = super().to_dict()
        data['registration_date'] = self.registration_date.isoformat() if self.registration_date else None
        data['favorite_routes'] = [item['route'].route_number for item in self.get_favorite_routes()[:3]]
        return data


class SeatReservation(BaseModel):
    """Seat reservation/booking for buses"""
    __tablename__ = 'seat_reservations'
    
    # Reservation details
    reservation_code = db.Column(db.String(20), unique=True, nullable=False)
    passenger_id = db.Column(db.String(36), db.ForeignKey('passenger_info.id'), nullable=False)
    bus_id = db.Column(db.String(36), db.ForeignKey('buses.id'), nullable=False)
    route_id = db.Column(db.String(36), db.ForeignKey('routes.id'), nullable=False)
    
    # Journey details
    boarding_stop_id = db.Column(db.String(36), db.ForeignKey('bus_stops.id'), nullable=False)
    alighting_stop_id = db.Column(db.String(36), db.ForeignKey('bus_stops.id'), nullable=False)
    
    # Seat information
    seat_number = db.Column(db.String(10))  # A1, B2, etc. or null for standing
    is_wheelchair_seat = db.Column(db.Boolean, default=False)
    is_priority_seat = db.Column(db.Boolean, default=False)
    
    # Timing
    reservation_time = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    scheduled_boarding_time = db.Column(db.DateTime, nullable=False)
    actual_boarding_time = db.Column(db.DateTime)
    actual_alighting_time = db.Column(db.DateTime)
    
    # Status
    status = db.Column(db.String(20), default='ACTIVE')  # ACTIVE, BOARDED, COMPLETED, CANCELLED, NO_SHOW
    
    # Payment
    fare_amount = db.Column(db.Float, nullable=False)
    payment_status = db.Column(db.String(20), default='PENDING')  # PENDING, PAID, REFUNDED
    payment_method = db.Column(db.String(20))  # CASH, CARD, MOBILE, FREE
    
    # Special requirements
    requires_assistance = db.Column(db.Boolean, default=False)
    special_notes = db.Column(db.Text)
    
    # Relationships
    bus = db.relationship('Bus', lazy=True)
    route = db.relationship('Route', lazy=True)
    boarding_stop = db.relationship('BusStop', foreign_keys=[boarding_stop_id], lazy=True)
    alighting_stop = db.relationship('BusStop', foreign_keys=[alighting_stop_id], lazy=True)
    
    def __repr__(self):
        return f'<SeatReservation {self.reservation_code}>'
    
    def is_expired(self):
        """Check if reservation has expired"""
        if self.status in ['COMPLETED', 'CANCELLED']:
            return True
        
        # Reservation expires 30 minutes after scheduled boarding time
        from datetime import timedelta
        expiry_time = self.scheduled_boarding_time + timedelta(minutes=30)
        return datetime.utcnow() > expiry_time
    
    def can_be_cancelled(self):
        """Check if reservation can be cancelled"""
        if self.status in ['COMPLETED', 'CANCELLED']:
            return False
        
        # Can cancel up to 15 minutes before scheduled boarding
        from datetime import timedelta
        cancel_deadline = self.scheduled_boarding_time - timedelta(minutes=15)
        return datetime.utcnow() < cancel_deadline
    
    def calculate_journey_duration(self):
        """Calculate actual journey duration if completed"""
        if self.actual_boarding_time and self.actual_alighting_time:
            duration = self.actual_alighting_time - self.actual_boarding_time
            return duration.total_seconds() / 60  # Return minutes
        return None
    
    def get_estimated_boarding_time(self):
        """Get estimated boarding time based on current bus location"""
        if self.status != 'ACTIVE':
            return None
        
        # Get current bus location and calculate ETA to boarding stop
        current_location = self.bus.get_current_location()
        if not current_location:
            return self.scheduled_boarding_time
        
        # Calculate distance and estimated time
        distance = self.boarding_stop.calculate_distance_to(
            current_location.latitude, 
            current_location.longitude
        )
        
        # Assume average speed of 25 km/h in city traffic
        estimated_minutes = (distance / 25) * 60
        from datetime import timedelta
        return datetime.utcnow() + timedelta(minutes=estimated_minutes)
    
    def to_dict(self):
        """Convert to dictionary with additional computed fields"""
        data = super().to_dict()
        data['reservation_time'] = self.reservation_time.isoformat() if self.reservation_time else None
        data['scheduled_boarding_time'] = self.scheduled_boarding_time.isoformat() if self.scheduled_boarding_time else None
        data['actual_boarding_time'] = self.actual_boarding_time.isoformat() if self.actual_boarding_time else None
        data['actual_alighting_time'] = self.actual_alighting_time.isoformat() if self.actual_alighting_time else None
        
        data['is_expired'] = self.is_expired()
        data['can_be_cancelled'] = self.can_be_cancelled()
        data['journey_duration_minutes'] = self.calculate_journey_duration()
        
        estimated_boarding = self.get_estimated_boarding_time()
        data['estimated_boarding_time'] = estimated_boarding.isoformat() if estimated_boarding else None
        
        # Add related object info
        if self.boarding_stop:
            data['boarding_stop_info'] = {
                'stop_code': self.boarding_stop.stop_code,
                'stop_name': self.boarding_stop.stop_name
            }
        
        if self.alighting_stop:
            data['alighting_stop_info'] = {
                'stop_code': self.alighting_stop.stop_code,
                'stop_name': self.alighting_stop.stop_name
            }
        
        return data
