from flask import Blueprint, request, jsonify
from ..models.stop import BusStop

stops_bp = Blueprint('stops', __name__)

@stops_bp.route('/', methods=['GET'])
def get_all_stops():
    """Get all active bus stops"""
    try:
        stops = BusStop.get_all_active()
        return jsonify({
            'success': True,
            'data': [stop.to_dict() for stop in stops],
            'count': len(stops)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@stops_bp.route('/<stop_id>', methods=['GET'])
def get_stop(stop_id):
    """Get specific stop details"""
    try:
        stop = BusStop.get_by_id(stop_id)
        if not stop:
            return jsonify({'success': False, 'error': 'Stop not found'}), 404
        
        return jsonify({
            'success': True,
            'data': stop.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@stops_bp.route('/<stop_id>/arrivals', methods=['GET'])
def get_stop_arrivals(stop_id):
    """Get next bus arrivals at a stop"""
    try:
        stop = BusStop.get_by_id(stop_id)
        if not stop:
            return jsonify({'success': False, 'error': 'Stop not found'}), 404
        
        limit = request.args.get('limit', 5, type=int)
        arrivals = stop.get_next_arrivals(limit)
        
        return jsonify({
            'success': True,
            'data': arrivals,
            'count': len(arrivals)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@stops_bp.route('/nearby', methods=['GET'])
def get_nearby_stops():
    """Get stops near a specific location"""
    try:
        latitude = request.args.get('latitude', type=float)
        longitude = request.args.get('longitude', type=float)
        radius_km = request.args.get('radius', 1.0, type=float)
        
        if latitude is None or longitude is None:
            return jsonify({'success': False, 'error': 'Latitude and longitude required'}), 400
        
        all_stops = BusStop.get_all_active()
        nearby_stops = []
        
        for stop in all_stops:
            distance = stop.calculate_distance_to(latitude, longitude)
            if distance <= radius_km:
                stop_data = stop.to_dict()
                stop_data['distance_km'] = round(distance, 2)
                nearby_stops.append(stop_data)
        
        # Sort by distance
        nearby_stops.sort(key=lambda x: x['distance_km'])
        
        return jsonify({
            'success': True,
            'data': nearby_stops,
            'count': len(nearby_stops)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@stops_bp.route('/search', methods=['GET'])
def search_stops():
    """Search stops by name or code"""
    try:
        query = request.args.get('q', '').strip()
        if not query:
            return jsonify({'success': False, 'error': 'Search query required'}), 400
        
        stops = BusStop.query.filter(
            (BusStop.stop_code.ilike(f'%{query}%')) |
            (BusStop.stop_name.ilike(f'%{query}%')) |
            (BusStop.address.ilike(f'%{query}%')) |
            (BusStop.landmark.ilike(f'%{query}%')),
            BusStop.is_active == True
        ).all()
        
        return jsonify({
            'success': True,
            'data': [stop.to_dict() for stop in stops],
            'count': len(stops)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
