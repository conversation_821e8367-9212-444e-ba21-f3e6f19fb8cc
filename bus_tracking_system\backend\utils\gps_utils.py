import math
import random
from typing import List, Tuple, Dict
from datetime import datetime, timedelta

class GPSUtils:
    """Utility functions for GPS operations"""
    
    EARTH_RADIUS_KM = 6371.0
    
    @staticmethod
    def haversine_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """
        Calculate the great circle distance between two points on Earth
        Returns distance in kilometers
        """
        # Convert latitude and longitude from degrees to radians
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        # Haversine formula
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        
        a = (math.sin(dlat/2)**2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return GPSUtils.EARTH_RADIUS_KM * c
    
    @staticmethod
    def calculate_bearing(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """
        Calculate the bearing between two GPS coordinates
        Returns bearing in degrees (0-360)
        """
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        dlon_rad = math.radians(lon2 - lon1)
        
        y = math.sin(dlon_rad) * math.cos(lat2_rad)
        x = (math.cos(lat1_rad) * math.sin(lat2_rad) - 
             math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(dlon_rad))
        
        bearing_rad = math.atan2(y, x)
        bearing_deg = math.degrees(bearing_rad)
        
        return (bearing_deg + 360) % 360
    
    @staticmethod
    def destination_point(lat: float, lon: float, bearing: float, distance_km: float) -> Tuple[float, float]:
        """
        Calculate destination point given start point, bearing and distance
        Returns (latitude, longitude) tuple
        """
        lat_rad = math.radians(lat)
        lon_rad = math.radians(lon)
        bearing_rad = math.radians(bearing)
        
        angular_distance = distance_km / GPSUtils.EARTH_RADIUS_KM
        
        dest_lat_rad = math.asin(
            math.sin(lat_rad) * math.cos(angular_distance) +
            math.cos(lat_rad) * math.sin(angular_distance) * math.cos(bearing_rad)
        )
        
        dest_lon_rad = lon_rad + math.atan2(
            math.sin(bearing_rad) * math.sin(angular_distance) * math.cos(lat_rad),
            math.cos(angular_distance) - math.sin(lat_rad) * math.sin(dest_lat_rad)
        )
        
        return math.degrees(dest_lat_rad), math.degrees(dest_lon_rad)
    
    @staticmethod
    def is_valid_coordinate(latitude: float, longitude: float) -> bool:
        """Validate GPS coordinates"""
        return (-90 <= latitude <= 90) and (-180 <= longitude <= 180)
    
    @staticmethod
    def calculate_speed(lat1: float, lon1: float, time1: datetime,
                       lat2: float, lon2: float, time2: datetime) -> float:
        """
        Calculate speed between two GPS points
        Returns speed in km/h
        """
        distance_km = GPSUtils.haversine_distance(lat1, lon1, lat2, lon2)
        time_diff = abs((time2 - time1).total_seconds())
        
        if time_diff == 0:
            return 0.0
        
        speed_ms = (distance_km * 1000) / time_diff  # m/s
        speed_kmh = speed_ms * 3.6  # Convert to km/h
        
        return speed_kmh
    
    @staticmethod
    def generate_route_coordinates(start_lat: float, start_lon: float,
                                 end_lat: float, end_lon: float,
                                 num_points: int = 10) -> List[Tuple[float, float]]:
        """
        Generate intermediate coordinates for a route
        Returns list of (latitude, longitude) tuples
        """
        coordinates = []
        
        for i in range(num_points + 1):
            ratio = i / num_points
            
            # Simple linear interpolation (for demonstration)
            lat = start_lat + (end_lat - start_lat) * ratio
            lon = start_lon + (end_lon - start_lon) * ratio
            
            # Add some random variation to make it more realistic
            lat += random.uniform(-0.001, 0.001)
            lon += random.uniform(-0.001, 0.001)
            
            coordinates.append((lat, lon))
        
        return coordinates
    
    @staticmethod
    def is_point_near_route(point_lat: float, point_lon: float,
                           route_coordinates: List[Tuple[float, float]],
                           threshold_km: float = 0.1) -> bool:
        """
        Check if a point is near a route
        Returns True if point is within threshold distance of any route segment
        """
        for i in range(len(route_coordinates) - 1):
            lat1, lon1 = route_coordinates[i]
            lat2, lon2 = route_coordinates[i + 1]
            
            # Calculate distance from point to line segment
            distance = GPSUtils._point_to_line_distance(
                point_lat, point_lon, lat1, lon1, lat2, lon2
            )
            
            if distance <= threshold_km:
                return True
        
        return False
    
    @staticmethod
    def _point_to_line_distance(px: float, py: float,
                               x1: float, y1: float,
                               x2: float, y2: float) -> float:
        """
        Calculate minimum distance from a point to a line segment
        Simplified calculation using Euclidean distance (for small distances)
        """
        # Convert to approximate meters for calculation
        px_m = px * 111000  # Rough conversion: 1 degree ≈ 111km
        py_m = py * 111000
        x1_m = x1 * 111000
        y1_m = y1 * 111000
        x2_m = x2 * 111000
        y2_m = y2 * 111000
        
        # Calculate distance from point to line segment
        A = px_m - x1_m
        B = py_m - y1_m
        C = x2_m - x1_m
        D = y2_m - y1_m
        
        dot = A * C + B * D
        len_sq = C * C + D * D
        
        if len_sq == 0:
            # Line segment is actually a point
            distance_m = math.sqrt(A * A + B * B)
        else:
            param = dot / len_sq
            
            if param < 0:
                xx = x1_m
                yy = y1_m
            elif param > 1:
                xx = x2_m
                yy = y2_m
            else:
                xx = x1_m + param * C
                yy = y1_m + param * D
            
            dx = px_m - xx
            dy = py_m - yy
            distance_m = math.sqrt(dx * dx + dy * dy)
        
        return distance_m / 1000  # Convert back to kilometers
    
    @staticmethod
    def get_bounding_box(coordinates: List[Tuple[float, float]], 
                        padding_km: float = 1.0) -> Dict[str, float]:
        """
        Get bounding box for a list of coordinates with padding
        Returns dict with min_lat, max_lat, min_lon, max_lon
        """
        if not coordinates:
            return {}
        
        lats = [coord[0] for coord in coordinates]
        lons = [coord[1] for coord in coordinates]
        
        min_lat = min(lats)
        max_lat = max(lats)
        min_lon = min(lons)
        max_lon = max(lons)
        
        # Add padding (rough conversion: 1km ≈ 0.009 degrees)
        padding_deg = padding_km * 0.009
        
        return {
            'min_lat': min_lat - padding_deg,
            'max_lat': max_lat + padding_deg,
            'min_lon': min_lon - padding_deg,
            'max_lon': max_lon + padding_deg
        }
    
    @staticmethod
    def simulate_gps_noise(lat: float, lon: float, accuracy_meters: float = 5.0) -> Tuple[float, float]:
        """
        Add realistic GPS noise to coordinates
        Returns (noisy_lat, noisy_lon)
        """
        # Convert accuracy to degrees (rough approximation)
        accuracy_deg = accuracy_meters / 111000
        
        # Add random noise within accuracy circle
        angle = random.uniform(0, 2 * math.pi)
        radius = random.uniform(0, accuracy_deg)
        
        noise_lat = radius * math.cos(angle)
        noise_lon = radius * math.sin(angle)
        
        return lat + noise_lat, lon + noise_lon
