from .base import BaseModel, db
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash, check_password_hash

class Driver(BaseModel):
    """Driver model for bus operators"""
    __tablename__ = 'drivers'
    
    # Personal Information
    employee_id = db.Column(db.String(20), unique=True, nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    phone_number = db.Column(db.String(20), nullable=False)
    email = db.Column(db.String(100))
    address = db.Column(db.Text)
    
    # Authentication
    password_hash = db.Column(db.String(255))
    last_login = db.Column(db.DateTime)
    
    # License Information
    license_number = db.Column(db.String(50), unique=True, nullable=False)
    license_type = db.Column(db.String(20), nullable=False)  # COMMERCIAL, HEAVY_VEHICLE, etc.
    license_expiry_date = db.Column(db.Date, nullable=False)
    
    # Employment Information
    hire_date = db.Column(db.Date, nullable=False)
    employment_status = db.Column(db.String(20), default='ACTIVE')  # ACTIVE, SUSPENDED, TERMINATED
    shift_type = db.Column(db.String(20), default='DAY')  # DAY, NIGHT, ROTATING
    
    # Performance and Safety
    safety_rating = db.Column(db.Float, default=5.0)  # 1-5 scale
    total_driving_hours = db.Column(db.Integer, default=0)
    accidents_count = db.Column(db.Integer, default=0)
    violations_count = db.Column(db.Integer, default=0)
    
    # Current Status
    current_status = db.Column(db.String(20), default='OFF_DUTY')  # ON_DUTY, OFF_DUTY, BREAK, SICK_LEAVE
    current_shift_start = db.Column(db.DateTime)
    current_shift_end = db.Column(db.DateTime)
    
    # Medical and Training
    medical_certificate_expiry = db.Column(db.Date)
    last_training_date = db.Column(db.Date)
    certifications = db.Column(db.JSON)  # List of additional certifications
    
    # Emergency Contact
    emergency_contact_name = db.Column(db.String(100))
    emergency_contact_phone = db.Column(db.String(20))
    emergency_contact_relationship = db.Column(db.String(50))
    
    def __repr__(self):
        return f'<Driver {self.employee_id}: {self.first_name} {self.last_name}>'
    
    @property
    def full_name(self):
        """Get driver's full name"""
        return f"{self.first_name} {self.last_name}"
    
    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check password"""
        return check_password_hash(self.password_hash, password)
    
    def is_license_valid(self):
        """Check if driver's license is still valid"""
        from datetime import date
        return self.license_expiry_date > date.today()
    
    def is_medical_certificate_valid(self):
        """Check if medical certificate is still valid"""
        if not self.medical_certificate_expiry:
            return False
        from datetime import date
        return self.medical_certificate_expiry > date.today()
    
    def can_drive(self):
        """Check if driver is eligible to drive"""
        return (
            self.employment_status == 'ACTIVE' and
            self.is_license_valid() and
            self.is_medical_certificate_valid() and
            self.current_status in ['ON_DUTY', 'BREAK']
        )
    
    def get_current_shift_duration(self):
        """Get current shift duration in hours"""
        if not self.current_shift_start:
            return 0
        
        end_time = self.current_shift_end or datetime.utcnow()
        duration = end_time - self.current_shift_start
        return duration.total_seconds() / 3600  # Return hours
    
    def is_shift_overtime(self, max_hours=8):
        """Check if current shift exceeds maximum hours"""
        return self.get_current_shift_duration() > max_hours
    
    def get_driving_history(self, days=30):
        """Get driving history for specified number of days"""
        from .tracking import BusStatus
        from datetime import date, timedelta
        
        start_date = date.today() - timedelta(days=days)
        
        return BusStatus.query.filter(
            BusStatus.driver_id == self.id,
            BusStatus.timestamp >= start_date,
            BusStatus.is_active == True
        ).order_by(db.desc(BusStatus.timestamp)).all()
    
    def calculate_performance_score(self):
        """Calculate overall performance score"""
        base_score = 100
        
        # Deduct points for accidents and violations
        accident_penalty = self.accidents_count * 10
        violation_penalty = self.violations_count * 5
        
        # Bonus for safety rating
        safety_bonus = (self.safety_rating - 3) * 5  # Bonus/penalty based on 3.0 baseline
        
        # Experience bonus
        experience_bonus = min(10, self.total_driving_hours / 1000)  # Max 10 points for experience
        
        score = base_score - accident_penalty - violation_penalty + safety_bonus + experience_bonus
        return max(0, min(100, score))  # Keep score between 0-100
    
    def needs_training_update(self, months=12):
        """Check if driver needs training update"""
        if not self.last_training_date:
            return True
        
        from datetime import date
        from dateutil.relativedelta import relativedelta
        
        next_training_due = self.last_training_date + relativedelta(months=months)
        return date.today() >= next_training_due
    
    def start_shift(self):
        """Start a new shift"""
        self.current_status = 'ON_DUTY'
        self.current_shift_start = datetime.utcnow()
        self.current_shift_end = None
        self.last_login = datetime.utcnow()
        self.save()
    
    def end_shift(self):
        """End current shift"""
        if self.current_shift_start:
            self.current_shift_end = datetime.utcnow()
            # Add to total driving hours
            shift_hours = self.get_current_shift_duration()
            self.total_driving_hours += int(shift_hours)
        
        self.current_status = 'OFF_DUTY'
        self.save()
    
    def to_dict(self):
        """Convert to dictionary with additional computed fields"""
        data = super().to_dict()
        
        # Remove sensitive information
        data.pop('password_hash', None)
        
        # Add computed fields
        data['full_name'] = self.full_name
        data['license_valid'] = self.is_license_valid()
        data['medical_certificate_valid'] = self.is_medical_certificate_valid()
        data['can_drive'] = self.can_drive()
        data['current_shift_duration_hours'] = round(self.get_current_shift_duration(), 2)
        data['is_overtime'] = self.is_shift_overtime()
        data['performance_score'] = round(self.calculate_performance_score(), 1)
        data['needs_training'] = self.needs_training_update()
        
        # Format dates
        data['hire_date'] = self.hire_date.isoformat() if self.hire_date else None
        data['license_expiry_date'] = self.license_expiry_date.isoformat() if self.license_expiry_date else None
        data['medical_certificate_expiry'] = self.medical_certificate_expiry.isoformat() if self.medical_certificate_expiry else None
        data['last_training_date'] = self.last_training_date.isoformat() if self.last_training_date else None
        data['last_login'] = self.last_login.isoformat() if self.last_login else None
        data['current_shift_start'] = self.current_shift_start.isoformat() if self.current_shift_start else None
        data['current_shift_end'] = self.current_shift_end.isoformat() if self.current_shift_end else None
        
        return data
