from flask import Blueprint, request, jsonify, current_app
from ..models.bus import Bus
from ..models.tracking import GPSLocation, BusStatus
from ..services.gps_service import GPSTrackingService
from datetime import datetime

buses_bp = Blueprint('buses', __name__)

@buses_bp.route('/', methods=['GET'])
def get_all_buses():
    """Get all active buses"""
    try:
        buses = Bus.get_all_active()
        return jsonify({
            'success': True,
            'data': [bus.to_dict() for bus in buses],
            'count': len(buses)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@buses_bp.route('/<bus_id>', methods=['GET'])
def get_bus(bus_id):
    """Get specific bus details"""
    try:
        bus = Bus.get_by_id(bus_id)
        if not bus:
            return jsonify({'success': False, 'error': 'Bus not found'}), 404
        
        return jsonify({
            'success': True,
            'data': bus.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@buses_bp.route('/<bus_id>/location', methods=['GET'])
def get_bus_location(bus_id):
    """Get current location of a bus"""
    try:
        bus = Bus.get_by_id(bus_id)
        if not bus:
            return jsonify({'success': False, 'error': 'Bus not found'}), 404
        
        gps_service = current_app.gps_service
        current_location = gps_service.get_bus_current_location(bus_id)
        
        if not current_location:
            return jsonify({'success': False, 'error': 'No location data available'}), 404
        
        return jsonify({
            'success': True,
            'data': current_location.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@buses_bp.route('/<bus_id>/location/history', methods=['GET'])
def get_bus_location_history(bus_id):
    """Get location history for a bus"""
    try:
        bus = Bus.get_by_id(bus_id)
        if not bus:
            return jsonify({'success': False, 'error': 'Bus not found'}), 404
        
        hours = request.args.get('hours', 24, type=int)
        gps_service = current_app.gps_service
        history = gps_service.get_bus_location_history(bus_id, hours)
        
        return jsonify({
            'success': True,
            'data': [location.to_dict() for location in history],
            'count': len(history)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@buses_bp.route('/<bus_id>/status', methods=['GET'])
def get_bus_status(bus_id):
    """Get current status of a bus"""
    try:
        bus = Bus.get_by_id(bus_id)
        if not bus:
            return jsonify({'success': False, 'error': 'Bus not found'}), 404
        
        current_status = bus.get_current_status()
        
        if not current_status:
            return jsonify({'success': False, 'error': 'No status data available'}), 404
        
        return jsonify({
            'success': True,
            'data': current_status.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@buses_bp.route('/<bus_id>/location', methods=['POST'])
def update_bus_location(bus_id):
    """Update bus location (for GPS devices or manual updates)"""
    try:
        bus = Bus.get_by_id(bus_id)
        if not bus:
            return jsonify({'success': False, 'error': 'Bus not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['latitude', 'longitude']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400
        
        gps_service = current_app.gps_service
        location = gps_service.record_gps_location(
            bus_id=bus_id,
            latitude=data['latitude'],
            longitude=data['longitude'],
            speed=data.get('speed', 0.0),
            heading=data.get('heading', 0.0),
            accuracy=data.get('accuracy', 5.0),
            source=data.get('source', 'GPS')
        )
        
        # Emit real-time update via WebSocket
        current_app.socketio.emit('bus_location_update', {
            'bus_id': bus_id,
            'location': location.to_dict()
        }, room=f'bus_{bus_id}')
        
        return jsonify({
            'success': True,
            'data': location.to_dict()
        })
    except ValueError as e:
        return jsonify({'success': False, 'error': str(e)}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@buses_bp.route('/<bus_id>/simulation/start', methods=['POST'])
def start_bus_simulation(bus_id):
    """Start GPS simulation for a bus"""
    try:
        bus = Bus.get_by_id(bus_id)
        if not bus:
            return jsonify({'success': False, 'error': 'Bus not found'}), 404
        
        if not bus.current_route_id:
            return jsonify({'success': False, 'error': 'Bus must be assigned to a route'}), 400
        
        data = request.get_json() or {}
        speed_kmh = data.get('speed_kmh', 30.0)
        
        gps_service = current_app.gps_service
        success = gps_service.start_gps_simulation(bus_id, bus.current_route_id, speed_kmh)
        
        if not success:
            return jsonify({'success': False, 'error': 'Failed to start simulation'}), 500
        
        return jsonify({
            'success': True,
            'message': f'GPS simulation started for bus {bus.bus_number}',
            'speed_kmh': speed_kmh
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@buses_bp.route('/<bus_id>/simulation/stop', methods=['POST'])
def stop_bus_simulation(bus_id):
    """Stop GPS simulation for a bus"""
    try:
        bus = Bus.get_by_id(bus_id)
        if not bus:
            return jsonify({'success': False, 'error': 'Bus not found'}), 404
        
        gps_service = current_app.gps_service
        success = gps_service.stop_gps_simulation(bus_id)
        
        return jsonify({
            'success': True,
            'message': f'GPS simulation stopped for bus {bus.bus_number}',
            'was_running': success
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@buses_bp.route('/route/<route_id>', methods=['GET'])
def get_buses_on_route(route_id):
    """Get all buses currently on a specific route"""
    try:
        buses = Bus.query.filter_by(
            current_route_id=route_id,
            is_active=True
        ).all()
        
        return jsonify({
            'success': True,
            'data': [bus.to_dict() for bus in buses],
            'count': len(buses)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@buses_bp.route('/nearby', methods=['GET'])
def get_nearby_buses():
    """Get buses near a specific location"""
    try:
        latitude = request.args.get('latitude', type=float)
        longitude = request.args.get('longitude', type=float)
        radius_km = request.args.get('radius', 5.0, type=float)
        
        if latitude is None or longitude is None:
            return jsonify({'success': False, 'error': 'Latitude and longitude required'}), 400
        
        gps_service = current_app.gps_service
        all_buses = Bus.query.filter_by(is_active=True).all()
        
        nearby_buses = []
        for bus in all_buses:
            current_location = gps_service.get_bus_current_location(bus.id)
            if current_location:
                distance = gps_service.calculate_distance(
                    latitude, longitude,
                    current_location.latitude, current_location.longitude
                )
                if distance <= radius_km:
                    bus_data = bus.to_dict()
                    bus_data['distance_km'] = round(distance, 2)
                    nearby_buses.append(bus_data)
        
        # Sort by distance
        nearby_buses.sort(key=lambda x: x['distance_km'])
        
        return jsonify({
            'success': True,
            'data': nearby_buses,
            'count': len(nearby_buses)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
