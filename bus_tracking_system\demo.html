<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPS-Based Bus Tracking System - Demo</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 3rem 2rem;
            border-radius: 24px;
            margin-bottom: 3rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
            background: linear-gradient(135deg, #fff, #e3f2fd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 24px 24px 0 0;
        }

        .card:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .card h3 i {
            color: #667eea;
            font-size: 1.5rem;
            padding: 12px;
            background: linear-gradient(135deg, #667eea20, #764ba220);
            border-radius: 16px;
        }
        .bus-item, .route-item, .stop-item, .arrival-item {
            padding: 20px;
            margin: 12px 0;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border-left: 4px solid #667eea;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .bus-item::before, .route-item::before, .stop-item::before, .arrival-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
            z-index: -1;
        }

        .bus-item:hover, .route-item:hover, .stop-item:hover, .arrival-item:hover {
            transform: translateX(8px) translateY(-4px);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.2);
            border-left-color: #764ba2;
        }

        .status {
            padding: 6px 16px;
            border-radius: 25px;
            color: white;
            font-size: 11px;
            font-weight: 700;
            background: linear-gradient(135deg, #00b894, #00cec9);
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 2px 10px rgba(0, 184, 148, 0.3);
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 14px 28px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            margin: 8px;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 0.95rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            background: linear-gradient(135deg, #764ba2, #667eea);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .alert-item {
            border-left-color: #fdcb6e;
            background: linear-gradient(135deg, rgba(253, 203, 110, 0.1), rgba(255, 179, 71, 0.1));
        }
        .live-indicator { display: inline-block; width: 10px; height: 10px; background: #28a745; border-radius: 50%; margin-right: 8px; animation: pulse 2s infinite; box-shadow: 0 0 10px rgba(40, 167, 69, 0.5); }
        @keyframes pulse { 0% { opacity: 1; transform: scale(1); } 50% { opacity: 0.7; transform: scale(1.1); } 100% { opacity: 1; transform: scale(1); } }
        .fade-in { animation: fadeIn 0.6s ease-in; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        .card { animation: fadeIn 0.6s ease-in; }
        .card:nth-child(1) { animation-delay: 0.1s; }
        .card:nth-child(2) { animation-delay: 0.2s; }
        .card:nth-child(3) { animation-delay: 0.3s; }
        .card:nth-child(4) { animation-delay: 0.4s; }
        .card:nth-child(5) { animation-delay: 0.5s; }
        .card:nth-child(6) { animation-delay: 0.6s; }
        @keyframes slideIn { from { transform: translateX(100%); opacity: 0; } to { transform: translateX(0); opacity: 1; } }
        @keyframes slideOut { from { transform: translateX(0); opacity: 1; } to { transform: translateX(100%); opacity: 0; } }

        .modern-bus-card {
            background: rgba(255, 255, 255, 0.9) !important;
            border: 1px solid rgba(102, 126, 234, 0.2) !important;
        }

        .bus-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        }

        .bus-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .bus-icon {
            font-size: 2rem;
            padding: 12px;
            background: linear-gradient(135deg, #667eea20, #764ba220);
            border-radius: 16px;
        }

        .bus-info h4 {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
        }

        .bus-model {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        .pulse {
            animation: statusPulse 2s infinite;
        }

        @keyframes statusPulse {
            0% { box-shadow: 0 0 0 0 rgba(0, 184, 148, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(0, 184, 148, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 184, 148, 0); }
        }

        .bus-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .metric {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .metric:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .metric-icon {
            font-size: 1.2rem;
        }

        .metric-label {
            font-size: 0.8rem;
            color: #666;
            font-weight: 500;
        }

        .metric-value {
            font-size: 0.95rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .occupancy-section {
            margin-top: 1rem;
        }

        .occupancy-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .occupancy-percent {
            color: #667eea;
            font-weight: 700;
        }

        .progress-container {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            height: 8px;
            overflow: hidden;
            position: relative;
        }

        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            border-radius: 12px;
            transition: width 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .floating-action {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
            border: none;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff6b6b;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .gradient-text {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .modern-shadow {
            box-shadow: 0 10px 40px rgba(102, 126, 234, 0.15);
        }

        @media (max-width: 768px) {
            .container { padding: 15px; }
            .header { padding: 2rem 1.5rem; }
            .header h1 { font-size: 2.2rem; }
            .grid { grid-template-columns: 1fr; gap: 1.5rem; }
            .search-section { padding: 2rem; }
            .quick-actions { justify-content: center; }
            .floating-action { bottom: 20px; right: 20px; }
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin: 3rem 0;
        }

        .feature {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .feature:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.15);
        }

        .feature i {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 1rem;
            padding: 20px;
            background: linear-gradient(135deg, #667eea20, #764ba220);
            border-radius: 20px;
            display: inline-block;
        }

        .feature h4 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .feature p {
            color: #666;
            font-size: 0.95rem;
            line-height: 1.6;
        }
        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 3rem;
            border-radius: 24px;
            margin: 3rem 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .demo-section h2 {
            color: #2c3e50;
            margin-bottom: 2rem;
            font-size: 1.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .demo-section h2 i {
            color: #667eea;
            padding: 12px;
            background: linear-gradient(135deg, #667eea20, #764ba220);
            border-radius: 16px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .stat-card:hover::before {
            transform: scale(1.2);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 2;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 0.5rem;
            font-weight: 500;
            position: relative;
            z-index: 2;
        }

        .stat-icon {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 2rem;
            opacity: 0.3;
            z-index: 1;
        }
        .search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 2.5rem;
            border-radius: 24px;
            margin: 3rem 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .search-section h2 {
            color: #2c3e50;
            margin-bottom: 2rem;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .search-box {
            display: flex;
            gap: 15px;
            margin-bottom: 2rem;
            position: relative;
        }

        .search-input {
            flex: 1;
            padding: 18px 24px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 16px;
            font-size: 1.1rem;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            font-weight: 400;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            background: white;
        }

        .search-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 18px 32px;
            border-radius: 16px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .quick-btn {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(102, 126, 234, 0.2);
            color: #2c3e50;
            padding: 12px 20px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quick-btn:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
            border-color: #667eea;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bus"></i> GPS-Based Bus Tracking System</h1>
            <p>Real-time Bus Tracking for Government Transportation</p>
            <p><span class="live-indicator"></span>Live Demo - Real-time Simulation</p>
            <div style="margin-top: 1rem; display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap;">
                <span style="background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px; font-size: 0.9rem;">
                    <i class="fas fa-shield-alt"></i> Secure & Safe
                </span>
                <span style="background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px; font-size: 0.9rem;">
                    <i class="fas fa-clock"></i> Real-time Updates
                </span>
                <span style="background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px; font-size: 0.9rem;">
                    <i class="fas fa-mobile-alt"></i> Mobile Friendly
                </span>
            </div>
        </div>

        <div class="search-section">
            <h2><i class="fas fa-search"></i> Find Your Journey</h2>
            <div class="search-box">
                <input type="text" class="search-input" placeholder="Search for routes, stops, or destinations..." id="searchInput">
                <button class="search-btn" onclick="performSearch()"><i class="fas fa-search"></i> Search</button>
            </div>
            <div class="quick-actions">
                <button class="quick-btn" onclick="findNearestStop()"><i class="fas fa-location-arrow"></i> Nearest Stop</button>
                <button class="quick-btn" onclick="showPopularRoutes()"><i class="fas fa-star"></i> Popular Routes</button>
                <button class="quick-btn" onclick="viewSchedule()"><i class="fas fa-calendar"></i> Schedule</button>
                <button class="quick-btn" onclick="emergencyContact()"><i class="fas fa-phone"></i> Emergency</button>
            </div>
        </div>

        <div class="demo-section">
            <h2><i class="fas fa-info-circle"></i> System Overview</h2>
            <p>Comprehensive GPS-based tracking system providing real-time arrival updates, seat availability, and route information for government buses.</p>

            <div class="feature-grid">
                <div class="feature">
                    <i class="fas fa-map-marked-alt"></i>
                    <h4>Real-time Tracking</h4>
                    <p>Live GPS updates every 30 seconds</p>
                </div>
                <div class="feature">
                    <i class="fas fa-clock"></i>
                    <h4>Arrival Predictions</h4>
                    <p>Accurate ETAs based on traffic</p>
                </div>
                <div class="feature">
                    <i class="fas fa-chair"></i>
                    <h4>Seat Availability</h4>
                    <p>Real-time seat count updates</p>
                </div>
                <div class="feature">
                    <i class="fas fa-shield-alt"></i>
                    <h4>Safety Features</h4>
                    <p>Emergency alerts and monitoring</p>
                </div>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3><i class="fas fa-bus"></i> Live Bus Tracking</h3>
                <div id="buses-data">
                    <div class="bus-item modern-bus-card">
                        <div class="bus-header">
                            <div class="bus-info">
                                <div class="bus-icon">🚌</div>
                                <div>
                                    <h4>BUS001</h4>
                                    <span class="bus-model">Volvo B7RLE</span>
                                </div>
                            </div>
                            <span class="status pulse">IN SERVICE</span>
                        </div>

                        <div class="bus-metrics">
                            <div class="metric">
                                <div class="metric-icon">📍</div>
                                <div>
                                    <div class="metric-label">Location</div>
                                    <div class="metric-value">Central Station</div>
                                </div>
                            </div>
                            <div class="metric">
                                <div class="metric-icon">🚀</div>
                                <div>
                                    <div class="metric-label">Speed</div>
                                    <div class="metric-value"><span id="bus1-speed">25</span> km/h</div>
                                </div>
                            </div>
                            <div class="metric">
                                <div class="metric-icon">💺</div>
                                <div>
                                    <div class="metric-label">Available</div>
                                    <div class="metric-value"><span id="bus1-seats">25</span>/40 seats</div>
                                </div>
                            </div>
                            <div class="metric">
                                <div class="metric-icon">🕒</div>
                                <div>
                                    <div class="metric-label">Updated</div>
                                    <div class="metric-value"><span id="bus1-time"></span></div>
                                </div>
                            </div>
                        </div>

                        <div class="occupancy-section">
                            <div class="occupancy-header">
                                <span>Seat Occupancy</span>
                                <span class="occupancy-percent">62.5%</span>
                            </div>
                            <div class="progress-container">
                                <div class="progress-bar" style="width: 62.5%;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="bus-item modern-bus-card">
                        <div class="bus-header">
                            <div class="bus-info">
                                <div class="bus-icon">🚌</div>
                                <div>
                                    <h4>BUS002</h4>
                                    <span class="bus-model">Tata Starbus</span>
                                </div>
                            </div>
                            <span class="status pulse">IN SERVICE</span>
                        </div>

                        <div class="bus-metrics">
                            <div class="metric">
                                <div class="metric-icon">📍</div>
                                <div>
                                    <div class="metric-label">Location</div>
                                    <div class="metric-value">City Mall</div>
                                </div>
                            </div>
                            <div class="metric">
                                <div class="metric-icon">🚀</div>
                                <div>
                                    <div class="metric-label">Speed</div>
                                    <div class="metric-value"><span id="bus2-speed">30</span> km/h</div>
                                </div>
                            </div>
                            <div class="metric">
                                <div class="metric-icon">💺</div>
                                <div>
                                    <div class="metric-label">Available</div>
                                    <div class="metric-value"><span id="bus2-seats">18</span>/35 seats</div>
                                </div>
                            </div>
                            <div class="metric">
                                <div class="metric-icon">🕒</div>
                                <div>
                                    <div class="metric-label">Updated</div>
                                    <div class="metric-value"><span id="bus2-time"></span></div>
                                </div>
                            </div>
                        </div>

                        <div class="occupancy-section">
                            <div class="occupancy-header">
                                <span>Seat Occupancy</span>
                                <span class="occupancy-percent">48.6%</span>
                            </div>
                            <div class="progress-container">
                                <div class="progress-bar" style="width: 48.6%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="btn" onclick="updateBusData()">🔄 Refresh Live Data</button>
            </div>

            <div class="card">
                <h3><i class="fas fa-route"></i> Routes</h3>
                <div class="route-item">
                    <strong>🛣️ R001: Central - Airport Express</strong><br>
                    <small>Central Station → Airport Road</small><br>
                    <small>Distance: 15.5 km | Duration: 45 min | Fare: ₹15</small><br>
                    <small>Frequency: Every 15 minutes</small>
                </div>
                <button class="btn" onclick="showRouteDetails()">📋 View Route Details</button>
            </div>

            <div class="card">
                <h3><i class="fas fa-map-marker-alt"></i> Bus Stops</h3>
                <div id="stops-data">
                    <div class="stop-item">
                        <strong>🚏 ST001: Central Station</strong><br>
                        <small>📍 (12.9716, 77.5946)</small><br>
                        <small>Facilities: Shelter, Digital Display</small>
                    </div>
                    <div class="stop-item">
                        <strong>🚏 ST002: City Mall</strong><br>
                        <small>📍 (12.9726, 77.5956)</small><br>
                        <small>Facilities: Shelter, Seating</small>
                    </div>
                </div>
                <button class="btn" onclick="showAllStops()">🗺️ View All Stops</button>
            </div>

            <div class="card">
                <h3><i class="fas fa-clock"></i> Next Arrivals</h3>
                <div id="arrivals-data">
                    <div class="arrival-item">
                        <strong>🚌 BUS001</strong> (Route R001)<br>
                        <small>⏰ Arriving in: <span id="arrival1"></span> minutes</small><br>
                        <small>💺 Available seats: 25</small>
                    </div>
                    <div class="arrival-item">
                        <strong>🚌 BUS002</strong> (Route R001)<br>
                        <small>⏰ Arriving in: <span id="arrival2"></span> minutes</small><br>
                        <small>💺 Available seats: 18</small>
                    </div>
                </div>
                <button class="btn" onclick="updateArrivals()">⏱️ Update Arrivals</button>
            </div>

            <div class="card">
                <h3><i class="fas fa-exclamation-triangle"></i> Service Alerts</h3>
                <div class="alert-item">
                    <strong>⚠️ Route R001 - Minor Delay</strong><br>
                    <small>Buses on Route R001 are experiencing 5-10 minute delays due to traffic.</small><br>
                    <small>Type: WARNING | Severity: MEDIUM</small>
                </div>
                <button class="btn" onclick="checkAlerts()">🔔 Check Latest Alerts</button>
            </div>

            <div class="card">
                <h3><i class="fas fa-cog"></i> System Status</h3>
                <div class="bus-item">
                    <strong>✅ System Status: Healthy</strong><br>
                    <small>📊 Database: Connected</small><br>
                    <small>🌐 API: Operational</small><br>
                    <small>📡 GPS Tracking: Active</small><br>
                    <small>🕒 Last Check: <span id="system-time"></span></small>
                </div>
                <button class="btn" onclick="checkSystemHealth()">🔍 System Health Check</button>
            </div>
        </div>

        <div class="demo-section">
            <h2><i class="fas fa-chart-line"></i> System Analytics</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">2</div>
                    <div class="stat-label">Active Buses</div>
                    <div class="stat-icon"><i class="fas fa-bus"></i></div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div class="stat-label">Bus Stops</div>
                    <div class="stat-icon"><i class="fas fa-map-marker-alt"></i></div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">43</div>
                    <div class="stat-label">Available Seats</div>
                    <div class="stat-icon"><i class="fas fa-chair"></i></div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">98%</div>
                    <div class="stat-label">On-Time Performance</div>
                    <div class="stat-icon"><i class="fas fa-clock"></i></div>
                </div>
            </div>
        </div>

        <!-- Floating Action Button -->
        <button class="floating-action" onclick="showQuickActions()" title="Quick Actions">
            <i class="fas fa-plus"></i>
            <span class="notification-badge">3</span>
        </button>
    </div>

    <script>
        // Simulate live data updates
        function updateTime() {
            const now = new Date().toLocaleTimeString();
            document.getElementById('bus1-time').textContent = now;
            document.getElementById('bus2-time').textContent = now;
            document.getElementById('system-time').textContent = now;
        }

        function updateArrivals() {
            const arrival1 = Math.floor(Math.random() * 10) + 2;
            const arrival2 = Math.floor(Math.random() * 10) + 2;
            document.getElementById('arrival1').textContent = arrival1;
            document.getElementById('arrival2').textContent = arrival2;
        }

        function updateBusData() {
            updateTime();

            // Simulate realistic speed changes
            const bus1Speed = Math.floor(Math.random() * 20) + 20; // 20-40 km/h
            const bus2Speed = Math.floor(Math.random() * 20) + 20;

            // Simulate seat changes
            const bus1Seats = Math.floor(Math.random() * 10) + 20; // 20-30 available
            const bus2Seats = Math.floor(Math.random() * 10) + 15; // 15-25 available

            document.getElementById('bus1-speed').textContent = bus1Speed;
            document.getElementById('bus2-speed').textContent = bus2Speed;
            document.getElementById('bus1-seats').textContent = bus1Seats;
            document.getElementById('bus2-seats').textContent = bus2Seats;

            // Update progress bars
            const bus1Occupancy = ((40 - bus1Seats) / 40) * 100;
            const bus2Occupancy = ((35 - bus2Seats) / 35) * 100;

            document.querySelector('.bus-item:nth-child(1) .progress-bar').style.width = bus1Occupancy + '%';
            document.querySelector('.bus-item:nth-child(2) .progress-bar').style.width = bus2Occupancy + '%';

            // Show success message
            showNotification('🔄 Bus data refreshed successfully!', 'success');
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 1000;
                background: ${type === 'success' ? '#28a745' : '#3498db'};
                color: white; padding: 12px 20px; border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        function showRouteDetails() {
            alert('🛣️ Route Details:\n\nR001: Central - Airport Express\n• 5 stops total\n• 15.5 km distance\n• 45 minutes duration\n• ₹15 base fare\n• Runs every 15 minutes\n• First bus: 06:00, Last bus: 22:00');
        }

        function showAllStops() {
            alert('🚏 All Bus Stops:\n\n1. ST001: Central Station\n2. ST002: City Mall\n3. ST003: Hospital Junction\n4. ST004: University Gate\n5. ST005: Airport Road\n\nEach stop has facilities like shelter, seating, and digital displays.');
        }

        function checkAlerts() {
            alert('🔔 Latest Service Alerts:\n\n⚠️ Route R001 - Minor Delay\nBuses experiencing 5-10 minute delays due to traffic.\n\n✅ All other routes operating normally.');
        }

        function checkSystemHealth() {
            alert('🔍 System Health Check:\n\n✅ API Server: Operational\n✅ Database: Connected\n✅ GPS Tracking: Active\n✅ WebSocket: Connected\n✅ All services running normally');
        }

        function performSearch() {
            const query = document.getElementById('searchInput').value;
            if (query.trim()) {
                alert(`🔍 Searching for: "${query}"\n\nFound results:\n• Route R001 matches your search\n• Central Station stop\n• City Mall stop\n\nIn the full system, this would show real search results!`);
            } else {
                alert('Please enter a search term');
            }
        }

        function findNearestStop() {
            alert('📍 Finding nearest stop...\n\n🚏 Nearest Stop: Central Station\n📍 Distance: 0.3 km\n⏰ Next bus: 3 minutes\n🚶‍♂️ Walking time: 4 minutes');
        }

        function showPopularRoutes() {
            alert('⭐ Popular Routes:\n\n🛣️ R001: Central - Airport Express\n👥 Most used route\n⏰ High frequency (every 15 min)\n💺 Good availability\n\n🛣️ R002: City Circle\n🔄 Circular route\n🏢 Connects business districts');
        }

        function viewSchedule() {
            alert('📅 Today\'s Schedule:\n\n🚌 Route R001:\n• First bus: 06:00 AM\n• Last bus: 10:00 PM\n• Frequency: Every 15 minutes\n• Peak hours: 07:00-09:00, 17:00-19:00\n\n⏰ Current time: ' + new Date().toLocaleTimeString());
        }

        function emergencyContact() {
            alert('🚨 Emergency Contacts:\n\n📞 Emergency Helpline: 100\n🚌 Bus Control Room: 1800-XXX-XXXX\n🚑 Medical Emergency: 108\n👮‍♂️ Police: 100\n\n⚠️ For immediate assistance, press the emergency button in the bus or contact the driver.');
        }

        function showQuickActions() {
            const actions = [
                '🚌 Track My Bus',
                '📍 Find Nearest Stop',
                '⏰ View Schedule',
                '🎫 Book Ticket',
                '📞 Emergency Help'
            ];

            const actionList = actions.map((action, index) => `${index + 1}. ${action}`).join('\n');
            alert(`⚡ Quick Actions:\n\n${actionList}\n\nSelect an action to continue...`);
        }

        // Initialize
        updateTime();
        updateArrivals();
        
        // Update times every 30 seconds
        setInterval(updateTime, 30000);
        
        // Update arrivals every minute
        setInterval(updateArrivals, 60000);
    </script>
</body>
</html>
