[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "advanced-design-project"
version = "2.0.0"
description = "Advanced platform with GPS bus tracking and AI translation"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "Development Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Development Team", email = "<EMAIL>"}
]
keywords = ["gps", "tracking", "translation", "ai", "fastapi", "react"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: JavaScript",
    "Framework :: FastAPI",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
]
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "sqlalchemy>=2.0.23",
    "asyncpg>=0.29.0",
    "redis>=5.0.1",
    "celery>=5.3.4",
    "prometheus-client>=0.19.0",
    "structlog>=23.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "mypy>=1.7.1",
    "flake8>=6.1.0",
    "bandit>=1.7.5",
    "pre-commit>=3.6.0",
    "sphinx>=7.2.6",
    "sphinx-rtd-theme>=1.3.0",
]
ml = [
    "torch>=2.1.1",
    "transformers>=4.36.2",
    "tensorflow>=2.15.0",
    "scikit-learn>=1.3.2",
    "pandas>=2.1.4",
    "numpy>=1.25.2",
    "sentence-transformers>=2.2.2",
]
monitoring = [
    "sentry-sdk>=1.38.0",
    "opentelemetry-api>=1.21.0",
    "opentelemetry-sdk>=1.21.0",
    "opentelemetry-instrumentation-fastapi>=0.42b0",
]
cloud = [
    "boto3>=1.34.0",
    "kubernetes>=28.1.0",
    "docker>=6.1.3",
]

[project.urls]
Homepage = "https://github.com/your-org/advanced-design-project"
Documentation = "https://advanced-design-project.readthedocs.io/"
Repository = "https://github.com/your-org/advanced-design-project.git"
"Bug Tracker" = "https://github.com/your-org/advanced-design-project/issues"
Changelog = "https://github.com/your-org/advanced-design-project/blob/main/CHANGELOG.md"

[project.scripts]
bus-tracking = "bus_tracking_system.main:main"
translator = "translator_tool.backend.main:main"

# Tool configurations
[tool.setuptools.packages.find]
where = ["."]
include = ["bus_tracking_system*", "translator_tool*"]
exclude = ["tests*", "docs*"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["bus_tracking_system", "translator_tool"]
known_third_party = ["fastapi", "sqlalchemy", "redis", "celery"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "redis.*",
    "celery.*",
    "prometheus_client.*",
    "transformers.*",
    "torch.*",
    "tensorflow.*",
    "sklearn.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=bus_tracking_system",
    "--cov=translator_tool",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=80",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests",
    "ml: marks tests that require ML models",
    "gpu: marks tests that require GPU",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

[tool.coverage.run]
source = ["bus_tracking_system", "translator_tool"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/venv/*",
    "*/env/*",
    "*/__pycache__/*",
    "*/node_modules/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "migrations", "venv", "env"]
skips = ["B101", "B601"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "migrations",
    "venv",
    "env",
    "build",
    "dist",
]
per-file-ignores = [
    "__init__.py:F401",
    "settings.py:E501",
]

[tool.pydocstyle]
convention = "google"
add-ignore = ["D100", "D101", "D102", "D103", "D104", "D105"]

[tool.commitizen]
name = "cz_conventional_commits"
version = "2.0.0"
tag_format = "v$version"
version_files = [
    "pyproject.toml:version",
    "bus_tracking_system/__init__.py:__version__",
    "translator_tool/__init__.py:__version__",
]

[tool.semantic_release]
version_variable = "pyproject.toml:version"
build_command = "pip install build && python -m build"
dist_path = "dist/"
upload_to_pypi = false
upload_to_release = true
commit_subject = "chore(release): v{version}"
commit_message = "chore(release): v{version}\n\nAutomatically generated by python-semantic-release"
