from flask import Blueprint, request, jsonify, current_app
from ..models.tracking import GPSLocation, BusStatus
from ..models.bus import Bus
from datetime import datetime

tracking_bp = Blueprint('tracking', __name__)

@tracking_bp.route('/live', methods=['GET'])
def get_live_tracking():
    """Get live tracking data for all active buses"""
    try:
        buses = Bus.query.filter_by(status='IN_SERVICE', is_active=True).all()
        live_data = []
        
        gps_service = current_app.gps_service
        
        for bus in buses:
            current_location = gps_service.get_bus_current_location(bus.id)
            current_status = bus.get_current_status()
            
            if current_location:
                bus_data = {
                    'bus_id': bus.id,
                    'bus_number': bus.bus_number,
                    'route_id': bus.current_route_id,
                    'location': current_location.to_dict(),
                    'status': current_status.to_dict() if current_status else None
                }
                live_data.append(bus_data)
        
        return jsonify({
            'success': True,
            'data': live_data,
            'count': len(live_data),
            'timestamp': current_location.timestamp.isoformat() if current_location else None
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@tracking_bp.route('/route/<route_id>', methods=['GET'])
def get_route_tracking(route_id):
    """Get live tracking data for buses on a specific route"""
    try:
        buses = Bus.query.filter_by(
            current_route_id=route_id,
            status='IN_SERVICE',
            is_active=True
        ).all()
        
        route_data = []
        gps_service = current_app.gps_service
        
        for bus in buses:
            current_location = gps_service.get_bus_current_location(bus.id)
            current_status = bus.get_current_status()
            
            if current_location:
                bus_data = {
                    'bus_id': bus.id,
                    'bus_number': bus.bus_number,
                    'location': current_location.to_dict(),
                    'status': current_status.to_dict() if current_status else None
                }
                route_data.append(bus_data)
        
        return jsonify({
            'success': True,
            'data': route_data,
            'count': len(route_data)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@tracking_bp.route('/arrivals/<stop_id>', methods=['GET'])
def get_arrival_predictions(stop_id):
    """Get arrival predictions for a specific stop"""
    try:
        from ..models.stop import BusStop
        
        stop = BusStop.get_by_id(stop_id)
        if not stop:
            return jsonify({'success': False, 'error': 'Stop not found'}), 404
        
        limit = request.args.get('limit', 5, type=int)
        arrivals = stop.get_next_arrivals(limit)
        
        return jsonify({
            'success': True,
            'data': arrivals,
            'count': len(arrivals),
            'stop_info': {
                'stop_code': stop.stop_code,
                'stop_name': stop.stop_name
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@tracking_bp.route('/eta', methods=['POST'])
def calculate_eta():
    """Calculate estimated time of arrival"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400
        
        bus_id = data.get('bus_id')
        stop_id = data.get('stop_id')
        
        if not bus_id or not stop_id:
            return jsonify({'success': False, 'error': 'bus_id and stop_id required'}), 400
        
        gps_service = current_app.gps_service
        eta = gps_service.estimate_arrival_time(bus_id, stop_id)
        
        if not eta:
            return jsonify({'success': False, 'error': 'Could not calculate ETA'}), 404
        
        return jsonify({
            'success': True,
            'data': {
                'bus_id': bus_id,
                'stop_id': stop_id,
                'estimated_arrival': eta.isoformat(),
                'estimated_minutes': round((eta - datetime.utcnow()).total_seconds() / 60)
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
