from .base import BaseModel, db
from datetime import datetime

class Alert(BaseModel):
    """General alert system for notifications and announcements"""
    __tablename__ = 'alerts'
    
    # Alert Information
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    alert_type = db.Column(db.String(20), nullable=False)  # INFO, WARNING, EMERGENCY, MAINTENANCE
    severity = db.Column(db.String(20), default='MEDIUM')  # LOW, MEDIUM, HIGH, CRITICAL
    
    # Targeting
    target_audience = db.Column(db.String(20), default='ALL')  # ALL, PASSENGERS, DRIVERS, ADMIN
    route_ids = db.Column(db.JSON)  # Specific routes affected (null = all routes)
    stop_ids = db.Column(db.JSON)  # Specific stops affected (null = all stops)
    bus_ids = db.Column(db.JSON)  # Specific buses affected (null = all buses)
    
    # Timing
    start_time = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    end_time = db.Column(db.DateTime)  # null = indefinite
    
    # Status
    status = db.Column(db.String(20), default='ACTIVE')  # ACTIVE, RESOLVED, CANCELLED
    
    # Source
    created_by = db.Column(db.String(36))  # User ID who created the alert
    source = db.Column(db.String(50), default='MANUAL')  # MANUAL, SYSTEM, AUTOMATIC
    
    # Additional Information
    action_required = db.Column(db.Boolean, default=False)
    external_link = db.Column(db.String(500))  # Link to more information
    contact_info = db.Column(db.String(200))  # Contact for more information
    
    def __repr__(self):
        return f'<Alert {self.alert_type}: {self.title}>'
    
    def is_active(self):
        """Check if alert is currently active"""
        if self.status != 'ACTIVE':
            return False
        
        now = datetime.utcnow()
        if now < self.start_time:
            return False
        
        if self.end_time and now > self.end_time:
            return False
        
        return True
    
    def affects_route(self, route_id):
        """Check if alert affects a specific route"""
        if not self.route_ids:  # null means all routes
            return True
        return route_id in self.route_ids
    
    def affects_stop(self, stop_id):
        """Check if alert affects a specific stop"""
        if not self.stop_ids:  # null means all stops
            return True
        return stop_id in self.stop_ids
    
    def affects_bus(self, bus_id):
        """Check if alert affects a specific bus"""
        if not self.bus_ids:  # null means all buses
            return True
        return bus_id in self.bus_ids
    
    def get_affected_entities(self):
        """Get human-readable list of affected entities"""
        affected = []
        
        if self.route_ids:
            from .route import Route
            routes = Route.query.filter(Route.id.in_(self.route_ids)).all()
            affected.extend([f"Route {r.route_number}" for r in routes])
        
        if self.stop_ids:
            from .stop import BusStop
            stops = BusStop.query.filter(BusStop.id.in_(self.stop_ids)).all()
            affected.extend([f"Stop {s.stop_code}" for s in stops])
        
        if self.bus_ids:
            from .bus import Bus
            buses = Bus.query.filter(Bus.id.in_(self.bus_ids)).all()
            affected.extend([f"Bus {b.bus_number}" for b in buses])
        
        return affected if affected else ["All services"]
    
    def resolve(self, resolved_by=None):
        """Mark alert as resolved"""
        self.status = 'RESOLVED'
        self.end_time = datetime.utcnow()
        if resolved_by:
            self.created_by = resolved_by  # Track who resolved it
        self.save()
    
    def to_dict(self):
        """Convert to dictionary with additional computed fields"""
        data = super().to_dict()
        data['start_time'] = self.start_time.isoformat() if self.start_time else None
        data['end_time'] = self.end_time.isoformat() if self.end_time else None
        data['is_currently_active'] = self.is_active()
        data['affected_entities'] = self.get_affected_entities()
        return data


class EmergencyAlert(BaseModel):
    """Emergency alert system for critical situations"""
    __tablename__ = 'emergency_alerts'
    
    # Emergency Information
    emergency_type = db.Column(db.String(30), nullable=False)  # ACCIDENT, BREAKDOWN, MEDICAL, SECURITY, FIRE
    description = db.Column(db.Text, nullable=False)
    severity_level = db.Column(db.Integer, nullable=False)  # 1-5 scale (5 = most critical)
    
    # Location Information
    bus_id = db.Column(db.String(36), db.ForeignKey('buses.id'))
    route_id = db.Column(db.String(36), db.ForeignKey('routes.id'))
    stop_id = db.Column(db.String(36), db.ForeignKey('bus_stops.id'))
    latitude = db.Column(db.Float)
    longitude = db.Column(db.Float)
    location_description = db.Column(db.String(200))
    
    # People Involved
    driver_id = db.Column(db.String(36), db.ForeignKey('drivers.id'))
    passenger_count = db.Column(db.Integer, default=0)
    injuries_reported = db.Column(db.Boolean, default=False)
    casualties_count = db.Column(db.Integer, default=0)
    
    # Response Information
    emergency_services_notified = db.Column(db.Boolean, default=False)
    police_notified = db.Column(db.Boolean, default=False)
    medical_services_notified = db.Column(db.Boolean, default=False)
    fire_services_notified = db.Column(db.Boolean, default=False)
    
    # Status and Timeline
    status = db.Column(db.String(20), default='ACTIVE')  # ACTIVE, RESPONDING, RESOLVED, FALSE_ALARM
    reported_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    first_responder_arrival = db.Column(db.DateTime)
    resolved_at = db.Column(db.DateTime)
    
    # Contact and Communication
    reporter_name = db.Column(db.String(100))
    reporter_phone = db.Column(db.String(20))
    incident_number = db.Column(db.String(50), unique=True)
    
    # Follow-up
    requires_investigation = db.Column(db.Boolean, default=False)
    insurance_claim_needed = db.Column(db.Boolean, default=False)
    media_attention = db.Column(db.Boolean, default=False)
    
    # Relationships
    bus = db.relationship('Bus', lazy=True)
    route = db.relationship('Route', lazy=True)
    stop = db.relationship('BusStop', lazy=True)
    driver = db.relationship('Driver', lazy=True)
    
    def __repr__(self):
        return f'<EmergencyAlert {self.emergency_type}: {self.incident_number}>'
    
    def generate_incident_number(self):
        """Generate unique incident number"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        self.incident_number = f"EMG-{timestamp}-{self.emergency_type[:3].upper()}"
    
    def get_response_time_minutes(self):
        """Calculate response time in minutes"""
        if not self.first_responder_arrival:
            return None
        
        response_time = self.first_responder_arrival - self.reported_at
        return response_time.total_seconds() / 60
    
    def get_resolution_time_minutes(self):
        """Calculate total resolution time in minutes"""
        if not self.resolved_at:
            return None
        
        resolution_time = self.resolved_at - self.reported_at
        return resolution_time.total_seconds() / 60
    
    def notify_emergency_services(self):
        """Mark that emergency services have been notified"""
        self.emergency_services_notified = True
        
        # Auto-notify specific services based on emergency type
        if self.emergency_type in ['ACCIDENT', 'MEDICAL']:
            self.medical_services_notified = True
            self.police_notified = True
        elif self.emergency_type == 'FIRE':
            self.fire_services_notified = True
            self.police_notified = True
        elif self.emergency_type == 'SECURITY':
            self.police_notified = True
        
        self.save()
    
    def escalate_severity(self):
        """Escalate emergency severity"""
        if self.severity_level < 5:
            self.severity_level += 1
            self.save()
    
    def resolve_emergency(self, resolution_notes=None):
        """Mark emergency as resolved"""
        self.status = 'RESOLVED'
        self.resolved_at = datetime.utcnow()
        if resolution_notes:
            self.description += f"\n\nResolution: {resolution_notes}"
        self.save()
    
    def is_critical(self):
        """Check if emergency is critical (severity 4-5)"""
        return self.severity_level >= 4
    
    def requires_immediate_response(self):
        """Check if emergency requires immediate response"""
        return (
            self.severity_level >= 3 and
            self.status == 'ACTIVE' and
            not self.emergency_services_notified
        )
    
    def to_dict(self):
        """Convert to dictionary with additional computed fields"""
        data = super().to_dict()
        data['reported_at'] = self.reported_at.isoformat() if self.reported_at else None
        data['first_responder_arrival'] = self.first_responder_arrival.isoformat() if self.first_responder_arrival else None
        data['resolved_at'] = self.resolved_at.isoformat() if self.resolved_at else None
        
        data['response_time_minutes'] = self.get_response_time_minutes()
        data['resolution_time_minutes'] = self.get_resolution_time_minutes()
        data['is_critical'] = self.is_critical()
        data['requires_immediate_response'] = self.requires_immediate_response()
        
        # Add location information
        if self.bus:
            data['bus_info'] = {
                'bus_number': self.bus.bus_number,
                'license_plate': self.bus.license_plate
            }
        
        if self.route:
            data['route_info'] = {
                'route_number': self.route.route_number,
                'route_name': self.route.route_name
            }
        
        if self.stop:
            data['stop_info'] = {
                'stop_code': self.stop.stop_code,
                'stop_name': self.stop.stop_name
            }
        
        if self.driver:
            data['driver_info'] = {
                'employee_id': self.driver.employee_id,
                'full_name': self.driver.full_name
            }
        
        return data
