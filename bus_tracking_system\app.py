import os
from flask import Flask, jsonify, render_template
from flask_cors import CORS
from flask_socketio import SocketIO
from backend.config.config import config
from backend.models.base import db
from backend.models import *  # Import all models
from backend.services.gps_service import GPSTrackingService
import threading
import time
from datetime import datetime

def create_app(config_name=None):
    """Application factory pattern"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    db.init_app(app)
    CORS(app, origins=app.config['CORS_ORIGINS'])
    socketio = SocketIO(app, cors_allowed_origins=app.config['CORS_ORIGINS'])
    
    # Initialize GPS service
    gps_service = GPSTrackingService()
    
    # Register blueprints
    from backend.api.buses import buses_bp
    from backend.api.routes import routes_bp
    from backend.api.stops import stops_bp
    from backend.api.tracking import tracking_bp
    from backend.api.passengers import passengers_bp
    from backend.api.alerts import alerts_bp
    
    app.register_blueprint(buses_bp, url_prefix='/api/buses')
    app.register_blueprint(routes_bp, url_prefix='/api/routes')
    app.register_blueprint(stops_bp, url_prefix='/api/stops')
    app.register_blueprint(tracking_bp, url_prefix='/api/tracking')
    app.register_blueprint(passengers_bp, url_prefix='/api/passengers')
    app.register_blueprint(alerts_bp, url_prefix='/api/alerts')
    
    # Store services in app context
    app.gps_service = gps_service
    app.socketio = socketio
    
    @app.route('/')
    def index():
        return render_template('index.html')

    @app.route('/api')
    def api_info():
        return jsonify({
            'message': 'GPS-Based Bus Tracking System API',
            'version': '1.0.0',
            'status': 'running',
            'timestamp': datetime.utcnow().isoformat()
        })
    
    @app.route('/health')
    def health_check():
        """Health check endpoint"""
        try:
            # Check database connection
            db.session.execute('SELECT 1')
            db_status = 'healthy'
        except Exception as e:
            db_status = f'error: {str(e)}'
        
        return jsonify({
            'status': 'healthy' if db_status == 'healthy' else 'unhealthy',
            'database': db_status,
            'timestamp': datetime.utcnow().isoformat()
        })
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': 'Not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({'error': 'Internal server error'}), 500
    
    # WebSocket events for real-time updates
    @socketio.on('connect')
    def handle_connect():
        print('Client connected')
    
    @socketio.on('disconnect')
    def handle_disconnect():
        print('Client disconnected')
    
    @socketio.on('subscribe_bus')
    def handle_subscribe_bus(data):
        """Subscribe to updates for a specific bus"""
        bus_id = data.get('bus_id')
        if bus_id:
            # Join room for this bus
            from flask_socketio import join_room
            join_room(f'bus_{bus_id}')
            print(f'Client subscribed to bus {bus_id}')
    
    @socketio.on('subscribe_route')
    def handle_subscribe_route(data):
        """Subscribe to updates for a specific route"""
        route_id = data.get('route_id')
        if route_id:
            from flask_socketio import join_room
            join_room(f'route_{route_id}')
            print(f'Client subscribed to route {route_id}')
    
    def start_gps_simulation_background():
        """Background task to update GPS simulations"""
        with app.app_context():
            while True:
                try:
                    # Update all active simulations
                    for bus_id in list(gps_service.active_simulations.keys()):
                        gps_service.update_gps_simulation(bus_id)
                        
                        # Emit real-time update
                        current_location = gps_service.get_bus_current_location(bus_id)
                        if current_location:
                            socketio.emit('bus_location_update', {
                                'bus_id': bus_id,
                                'location': current_location.to_dict()
                            }, room=f'bus_{bus_id}')
                    
                    time.sleep(30)  # Update every 30 seconds
                except Exception as e:
                    print(f'Error in GPS simulation: {e}')
                    time.sleep(30)
    
    # Start background GPS simulation thread
    if config_name != 'testing':
        simulation_thread = threading.Thread(target=start_gps_simulation_background, daemon=True)
        simulation_thread.start()
    
    return app, socketio

def init_database(app):
    """Initialize database with tables and sample data"""
    with app.app_context():
        # Create all tables
        db.create_all()
        
        # Check if data already exists
        if Bus.query.first() is not None:
            print("Database already initialized")
            return
        
        print("Initializing database with sample data...")
        
        # Create sample bus stops
        stops_data = [
            {'stop_code': 'ST001', 'stop_name': 'Central Station', 'latitude': 12.9716, 'longitude': 77.5946, 'has_shelter': True, 'has_digital_display': True},
            {'stop_code': 'ST002', 'stop_name': 'City Mall', 'latitude': 12.9726, 'longitude': 77.5956, 'has_shelter': True, 'has_seating': True},
            {'stop_code': 'ST003', 'stop_name': 'Hospital Junction', 'latitude': 12.9736, 'longitude': 77.5966, 'has_shelter': True, 'has_wheelchair_access': True},
            {'stop_code': 'ST004', 'stop_name': 'University Gate', 'latitude': 12.9746, 'longitude': 77.5976, 'has_shelter': True, 'has_digital_display': True},
            {'stop_code': 'ST005', 'stop_name': 'Airport Road', 'latitude': 12.9756, 'longitude': 77.5986, 'has_shelter': True, 'has_seating': True}
        ]
        
        stops = []
        for stop_data in stops_data:
            stop = BusStop(**stop_data)
            stop.save()
            stops.append(stop)
        
        # Create sample route
        route = Route(
            route_number='R001',
            route_name='Central - Airport Express',
            start_location='Central Station',
            end_location='Airport Road',
            total_distance_km=15.5,
            estimated_duration_minutes=45,
            base_fare=15.0
        )
        route.save()
        
        # Add stops to route
        for i, stop in enumerate(stops):
            route_stop = RouteStop(
                route_id=route.id,
                stop_id=stop.id,
                stop_order=i + 1,
                estimated_arrival_time_offset=i * 10,  # 10 minutes between stops
                cumulative_distance_km=i * 3.1  # Roughly 3.1 km between stops
            )
            route_stop.save()
        
        # Create sample driver
        driver = Driver(
            employee_id='DRV001',
            first_name='John',
            last_name='Doe',
            phone_number='+**********',
            license_number='DL123456789',
            license_type='COMMERCIAL',
            license_expiry_date='2025-12-31',
            hire_date='2020-01-15'
        )
        driver.set_password('password123')
        driver.save()
        
        # Create sample buses
        buses_data = [
            {'bus_number': 'BUS001', 'license_plate': 'KA01AB1234', 'model': 'Volvo B7RLE', 'manufacturer': 'Volvo', 'year': 2020, 'total_seats': 40},
            {'bus_number': 'BUS002', 'license_plate': 'KA01AB1235', 'model': 'Tata Starbus', 'manufacturer': 'Tata', 'year': 2021, 'total_seats': 35},
            {'bus_number': 'BUS003', 'license_plate': 'KA01AB1236', 'model': 'Ashok Leyland', 'manufacturer': 'Ashok Leyland', 'year': 2019, 'total_seats': 45}
        ]
        
        for bus_data in buses_data:
            bus = Bus(**bus_data)
            bus.current_route_id = route.id
            bus.current_driver_id = driver.id
            bus.status = 'IN_SERVICE'
            bus.save()
        
        print("Database initialized successfully!")

if __name__ == '__main__':
    app, socketio = create_app()
    
    # Initialize database
    init_database(app)
    
    # Run the application
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
