<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Government Bus Tracking System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-bus"></i>
                <h1>Government Bus Tracker</h1>
            </div>
            <nav class="nav">
                <a href="#home" class="nav-link active">Home</a>
                <a href="#routes" class="nav-link">Routes</a>
                <a href="#stops" class="nav-link">Stops</a>
                <a href="#alerts" class="nav-link">Alerts</a>
            </nav>
        </div>
    </header>

    <main class="main">
        <!-- Home Section -->
        <section id="home" class="section active">
            <div class="container">
                <div class="hero">
                    <h2>Real-time Bus Tracking</h2>
                    <p>Track government buses in real-time, get arrival predictions, and plan your journey efficiently.</p>
                </div>

                <div class="search-section">
                    <div class="search-box">
                        <input type="text" id="searchInput" placeholder="Search for routes, stops, or buses...">
                        <button id="searchBtn"><i class="fas fa-search"></i></button>
                    </div>
                    <div class="location-btn">
                        <button id="locationBtn"><i class="fas fa-location-arrow"></i> Use My Location</button>
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="card">
                        <h3><i class="fas fa-map-marked-alt"></i> Live Map</h3>
                        <div id="map" class="map-container"></div>
                        <div class="map-controls">
                            <button id="showAllBuses" class="btn btn-primary">Show All Buses</button>
                            <button id="showNearbyStops" class="btn btn-secondary">Nearby Stops</button>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-clock"></i> Next Arrivals</h3>
                        <div id="nextArrivals" class="arrivals-list">
                            <p class="loading">Select a stop to see arrivals...</p>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-route"></i> Popular Routes</h3>
                        <div id="popularRoutes" class="routes-list">
                            <div class="loading">Loading routes...</div>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-exclamation-triangle"></i> Service Alerts</h3>
                        <div id="serviceAlerts" class="alerts-list">
                            <div class="loading">Loading alerts...</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Routes Section -->
        <section id="routes" class="section">
            <div class="container">
                <h2>Bus Routes</h2>
                <div class="routes-filter">
                    <input type="text" id="routeSearch" placeholder="Search routes...">
                    <select id="routeFilter">
                        <option value="">All Routes</option>
                        <option value="active">Active Only</option>
                        <option value="express">Express Routes</option>
                    </select>
                </div>
                <div id="routesList" class="routes-grid">
                    <div class="loading">Loading routes...</div>
                </div>
            </div>
        </section>

        <!-- Stops Section -->
        <section id="stops" class="section">
            <div class="container">
                <h2>Bus Stops</h2>
                <div class="stops-filter">
                    <input type="text" id="stopSearch" placeholder="Search stops...">
                    <button id="nearbyStopsBtn">Find Nearby Stops</button>
                </div>
                <div id="stopsList" class="stops-grid">
                    <div class="loading">Loading stops...</div>
                </div>
            </div>
        </section>

        <!-- Alerts Section -->
        <section id="alerts" class="section">
            <div class="container">
                <h2>Service Alerts</h2>
                <div class="alerts-filter">
                    <select id="alertFilter">
                        <option value="">All Alerts</option>
                        <option value="INFO">Information</option>
                        <option value="WARNING">Warnings</option>
                        <option value="EMERGENCY">Emergency</option>
                    </select>
                </div>
                <div id="alertsList" class="alerts-grid">
                    <div class="loading">Loading alerts...</div>
                </div>
            </div>
        </section>
    </main>

    <!-- Modal for detailed information -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="{{ url_for('static', filename='js/config.js') }}"></script>
    <script src="{{ url_for('static', filename='js/api.js') }}"></script>
    <script src="{{ url_for('static', filename='js/map.js') }}"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
