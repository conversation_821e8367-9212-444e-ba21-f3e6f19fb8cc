apiVersion: apps/v1
kind: Deployment
metadata:
  name: translator-api
  namespace: advanced-platform
  labels:
    app: translator-api
    version: v2.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: translator-api
  template:
    metadata:
      labels:
        app: translator-api
        version: v2.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8001"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: translator-api
        image: translator:v2.0.0
        ports:
        - containerPort: 8001
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: url
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: app-secret
              key: secret-key
        - name: ENVIRONMENT
          value: "production"
        - name: DEBUG
          value: "false"
        - name: LOG_LEVEL
          value: "INFO"
        - name: ML_MODEL_PATH
          value: "/app/models"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
            nvidia.com/gpu: 1
          limits:
            memory: "4Gi"
            cpu: "2000m"
            nvidia.com/gpu: 1
        livenessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: models
          mountPath: /app/models
        - name: uploads
          mountPath: /app/uploads
        - name: cache
          mountPath: /app/cache
      volumes:
      - name: models
        persistentVolumeClaim:
          claimName: translator-models-pvc
      - name: uploads
        persistentVolumeClaim:
          claimName: translator-uploads-pvc
      - name: cache
        emptyDir:
          sizeLimit: 1Gi
      nodeSelector:
        accelerator: nvidia-tesla-v100
      tolerations:
      - key: nvidia.com/gpu
        operator: Exists
        effect: NoSchedule
---
apiVersion: v1
kind: Service
metadata:
  name: translator-service
  namespace: advanced-platform
  labels:
    app: translator-api
spec:
  selector:
    app: translator-api
  ports:
  - name: http
    port: 80
    targetPort: 8001
    protocol: TCP
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: translator-frontend
  namespace: advanced-platform
  labels:
    app: translator-frontend
    version: v2.0.0
spec:
  replicas: 2
  selector:
    matchLabels:
      app: translator-frontend
  template:
    metadata:
      labels:
        app: translator-frontend
        version: v2.0.0
    spec:
      containers:
      - name: translator-frontend
        image: translator-frontend:v2.0.0
        ports:
        - containerPort: 80
          name: http
        env:
        - name: REACT_APP_API_URL
          value: "https://api.translator.example.com"
        - name: REACT_APP_WS_URL
          value: "wss://api.translator.example.com/ws"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: translator-frontend-service
  namespace: advanced-platform
  labels:
    app: translator-frontend
spec:
  selector:
    app: translator-frontend
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: translator-ingress
  namespace: advanced-platform
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/rate-limit: "200"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/websocket-services: "translator-service"
spec:
  tls:
  - hosts:
    - translator.example.com
    - api.translator.example.com
    secretName: translator-tls
  rules:
  - host: translator.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: translator-frontend-service
            port:
              number: 80
  - host: api.translator.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: translator-service
            port:
              number: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: translator-api-hpa
  namespace: advanced-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: translator-api
  minReplicas: 3
  maxReplicas: 8
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: translator-frontend-hpa
  namespace: advanced-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: translator-frontend
  minReplicas: 2
  maxReplicas: 6
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
